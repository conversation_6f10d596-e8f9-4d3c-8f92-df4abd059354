<!--
 * @Descripttion: 
 * @version: 
 * @Author: changqing
 * @Date: 2019-11-13 10:22:59
 * @LastEditors: changqing
 * @LastEditTime: 2020-09-27 11:23:20
 -->
<template>
    <div class="fillcontain " >
        <right-top></right-top>
        <p class="sysName tipName">请选择您将进入的后管：</p>

        <div class="sysBox " >
            <div v-show="roleIdList.indexOf(5) > -1">
                <transition name="form-fade" mode="in-out" >
                    <div class="box2-nav-box" v-show="show" @click="gotoSys('ztSys')">
                        <div class="box2-nav-box-img" ></div>
                        <div class="box2-nav-box-text">展台后管</div>
                    </div>
                </transition>
            </div>

            <div v-show="roleIdList.indexOf(9) > -1">
                <transition name="form-fade" mode="in-out" >
                    <div class="box2-nav-box" v-show="show" @click="gotoSys('activeSys')">
                        <div class="box2-nav-box-img" ></div>
                        <div class="box2-nav-box-text">答题活动后管</div>
                    </div>
                </transition>
            </div>

            <div v-if="roleIdList.indexOf(6) > -1">
                <transition name="form-fade" mode="in-out"  >
                    <div class="box2-nav-box" v-show="show"  @click="gotoSys('appSys')">
                        <div class="box2-nav-box-img box2-nav-box-img-2" ></div>
                        <div class="box2-nav-box-text">app后管</div>
                    </div>
                </transition>
            </div>
            <div v-if="roleIdList.indexOf(7) > -1">
                <transition name="form-fade" mode="in-out"  >
                    <div class="box2-nav-box" v-show="show"  @click="gotoSys('deappSys')">
                        <div class="box2-nav-box-img box2-nav-box-img-2" ></div>
                        <div class="box2-nav-box-text">app后管-外部运维</div>
                    </div>
                </transition>
            </div>
            <div v-show="roleIdList.indexOf(4) > -1">
                <transition name="form-fade" mode="in-out"  >
                    <div class="box2-nav-box" v-show="show"  @click="gotoSys('sxjy')">
                        <div class="box2-nav-box-img box2-nav-box-img-2" ></div>
                        <div class="box2-nav-box-text">舒心就医后管</div>
                    </div>
                </transition>
            </div>

            
            

        </div>
        

        <!-- <transition name="el-fade-in-linear">
            <div  class="transition-box">展台后管</div>
        </transition>

        <transition name="el-fade-in-linear">
            <div class="transition-box">app后管</div>
        </transition> -->

        
      
    </div>
  	
</template>

<script>
	import rightTop from "../components/rightTop"
	// import {mapActions, mapState} from 'vuex'

	export default {
	    data(){
			return {
                show:false,
                // roleList:[],
                roleIdList:[],
				
				
				
			}
        },
        components: {
          rightTop,
        },
        created(){
            debugger
            let roleList = [];
            this.roleIdList = [];
            if(window.localStorage.getItem("roleList")){
                roleList = JSON.parse(window.localStorage.getItem("roleList"));
            }else{
                this.$message({
                    type: 'error',
                    message: '未检测到权限列表，请重新登录'
                });
                this.$router.push('/');
            }

            roleList.forEach(it => {
                console.log(this);
                this.roleIdList.push(it.roleId);
            });

        },
		mounted(){
            
            


            this.show = true;

            
			// if (!this.adminInfo.id) {
    		// 	this.getAdminData()
    		// }
		},
		computed: {
			// ...mapState(['adminInfo']),
		},
		methods: {
            gotoSys(path){
                let obj = {
                    ztSys:"#/ztSys/appList",
                    appSys:"#/appSys/accountList",
                    sxjy:"/post-pay/#/order",
                    deappSys:"#/deappSys/accountList",
                    activeSys:"#/activeSys/activeList"
                };
                
                window.location.href = obj[path];
                return false;

            }
			
			
		},
		watch: {
		
		}
	}
</script>

<style lang="less" scoped>
    // .sysBg{
    //     background: url("http://fuwu.tz-wf.com/imgs/index/box2_bg.png") no-repeat center;
    //     background-size: cover;
    // }
    .sysBox{
        // width: 80%;
		// display: flex;
		// justify-content: space-around;
		// align-items: center;
        // margin: 10px auto;


        width: 80%;
        flex-flow: row wrap;
        display: -ms-flexbox;
        display: flex;
        justify-content: space-between;
        -ms-flex-align: center;
        align-items: center;
        margin: 10px auto;
    }
    .topDiv{
        width: 100%;
        height: 109px;
        background-color: #f7f7f7;
    }
    .sysItem{
        background-color: white;
        width: 200px;
        margin-left: 20px;
    }
    .sysName{
        font-size: 30px;
        color: #525252;
        text-align: center;
        margin-top: 26px;
        margin-bottom: 26px;
    }
    .tipName{
        text-align: left;
        /* width: 90%; */
        padding-left: 10%;
        // margin-top: 0;
    }


    .box2-nav-box {
        position: relative;
        width: 360px;
        height: 412px;
        background: #FFFFFF;
        border-radius: 6px;
        box-shadow: 0px 1px 20px 0px rgba(224,224,224,0.6);
        overflow: hidden;
        cursor: pointer;
        margin: 30px;
    }
    .box2-nav-box-img {
        width: 336px;
        height: 291px;
        margin: 0 auto;
        margin-top: 11px;
        border-radius: 8px;
        background: url("../img/box2_img1.png");
    }

    .box2-nav-box-img.box2-nav-box-img-2{
        background: url("../img/box2_img2.png");
    }


    .box2-nav-box-text {
        line-height: 106px;
        font-size: 30px;
        color: #333333;
        text-align: center;
    }

    .form-fade-enter-active, .form-fade-leave-active {
	  	transition: all 1s;
	}
	.form-fade-enter, .form-fade-leave-active {
	  	transform: translate3d(-50px, 0, -10px);
	  	opacity: 0;
	}
	
</style>
