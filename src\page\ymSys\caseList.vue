<template>
    <div class="fillcontain">
        <head-top></head-top>
        <div class="table_container">
          <div style="margin-bottom:10px;">
            <el-input v-model="title" placeholder="请输入标题" style="width:200px;" size="small"></el-input>
            
            <el-select v-model="status" placeholder="请选择状态" style="width:150px;" size="small">
              <el-option
                v-for="item in options_statusList"
                :key="item.typeKey"
                :label="item.typeValue"
                :value="item.typeKey">
              </el-option>
            </el-select>

            <el-date-picker size="small"
                v-model="timeCondition"
                type="daterange"
                align="right"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期" style="width:240px;margin-top:10px;" 
                :picker-options="pickerOptions2">
            </el-date-picker>
            
            <el-button type="primary" style="margin-left:10px;" icon="el-icon-search" size="small"  @click="search()">搜索</el-button>
            <!-- <el-button icon="el-icon-search" circle style="margin-left:10px;"></el-button> -->
            <el-button type="warning" size="small" icon="el-icon-refresh" @click="clearCondition()">重置并刷新</el-button>
            <!-- <el-button type="danger" style="float:right" icon="el-icon-remove" size="small"  @click="cancleYY()">已约取消</el-button> -->
            <br>
            <el-button type="primary" style="float:right;margin-bottom:10px;" icon="el-icon-plus" size="small"  @click="addNew()">新增</el-button>
          </div>




          
            <el-table
              size="small"
                :data="tableData"
                v-loading="loading"
                highlight-current-row
                stripe
                border
                :height="tableHeight"
                style="width:100%;">
                <el-table-column
                  type="index"
                  :index="oprIndex"
                  label="序号"
                  width="50">
                </el-table-column>
                <el-table-column
                  prop="title"
                  label="标题"
                  width="200"
                >
                </el-table-column>
                <el-table-column
                  prop="takeEffectTime"
                  label="生效时间"
                  width="150"
                  fixed="right"
                >
                </el-table-column>
                <el-table-column
                  prop="invalidTime"
                  label="失效时间"
                  width="150"
                  fixed="right"
                >
                </el-table-column>

                <el-table-column
                  label="状态"
                >
                    <template slot-scope="scope">
                    {{getTxt_(scope.row,"options_statusList","status")}}
                    </template>
                </el-table-column>

                
                
                <!-- <el-table-column
                  prop="tips"
                  label="关键词"
                >
                </el-table-column> -->

                <!-- <el-table-column
                  prop="nums"
                  label="排序"
                >
                </el-table-column> -->
                
                
                
                <el-table-column
                  prop=""
                  label="操作"
                  fixed="right"
                  width="150"
                >
                    <template slot-scope="scope">

                        
                        <el-button type="primary" icon="el-icon-view" circle @click="showZx(scope.row)" size="small"></el-button>
                        <el-button type="primary" icon="el-icon-edit" circle @click="edit(scope.row)" size="small"></el-button>
                        <el-button type="danger" icon="el-icon-delete" circle size="small" @click="rmApp(scope.row)"></el-button>
                        
                    </template>
                </el-table-column>
                
            </el-table>
            
            <div class="Pagination" style="text-align: left;margin-top: 10px;">
                <el-pagination
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage"
                  :page-sizes="[10, 20, 30, 40]"
                  :page-size="10"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="count">
                </el-pagination>
            </div>
            <el-dialog  :title= '(isEdit ? "修改":"新增")+( infoType === "1" ? "非法医美案例" : "医疗美容器械咨讯" )'
                ref="appDialog"
                :visible.sync="appFormVisible" 
                :close-on-click-modal="false"  
                :before-close="beforeClose"
                class="abow_dialog" >
              
                <el-form ref="form"
                    :model="form" label-width="120px" 
                    :rules="appFormrules" 
                    :validate-on-rule-change="false">
                    <el-form-item label="标题" prop="title">
                        <el-input v-model="form.title"></el-input>
                    </el-form-item>
                    
                    
                    <!-- <el-form-item label="所属类别" prop="infoType">
                        <el-select v-model="form.infoType" placeholder="请选择所属类别">
                            <el-option
                                v-for="item in options_infoTypeList"
                                :key="item.typeKey"
                                :label="item.typeValue"
                                :value="item.typeKey">
                            </el-option>
                        </el-select>
                    </el-form-item> -->

                    <!-- <el-form-item label="关键词" prop="tips">
                        <el-input v-model="form.tips"></el-input>
                    </el-form-item> -->

                    <el-form-item label="内容类型" prop="source">
                        <el-select v-model="form.source" placeholder="请选择内容类型">
                            <el-option
                                v-for="item in options_sourceTypeList"
                                :key="item.typeKey"
                                :label="item.typeValue"
                                :value="item.typeKey">
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="链接" prop="link" v-if="form.source === 0">
                        <el-input v-model="form.link"></el-input>
                    </el-form-item>
                    

                    <el-form-item label="内容" prop="body"  class="is-required" v-show="form.source !== 0">
                        <!-- <div id="editor_zx" ></div> -->
                        <textarea id="editor_zx" name="content" style="width:100%;height:300px;">
                        </textarea>
                    </el-form-item>
                    <el-form-item label="主图类型" prop="imageType">
                        <el-select v-model="form.imageType" placeholder="请选择主图类别">
                            <el-option
                                v-for="item in options_imageTypeList"
                                :key="item.typeKey"
                                :label="item.typeValue"
                                :value="item.typeKey">
                            </el-option>
                        </el-select>
                        <span v-if="form.imageType === 1">
                            （小图宽高比：113:80）
                        </span>
                        <span v-if="form.imageType === 2">
                            （大图宽高比：69:40）
                        </span>

                        
                    </el-form-item>

                    <el-form-item label="主图" prop="imageUrl" class="is-required" v-show="form.imageType !== 0">
                        <el-upload
                            class="avatar-uploader"
                            ref="upload"
                            action="/yimei/back/information/uploadPicture"
                            :headers="img_headers"
                            :on-change = "changeImg"
                            :show-file-list="false"
                            :on-success="imgSuccess"
                            :on-error="imgError"
                            :auto-upload="false"
                            :before-upload="beforeAvatarUpload"
                            >
                        
                            <img v-show="form.imageUrl" :src="imgPath_" class="avatar" id="img_apps" style="width: 100%;height: 100%;max-width: 300px;">
                            <i v-show="!form.imageUrl" class="el-icon-plus avatar-uploader-icon"></i>
                        </el-upload>
                        
                        <div class="el-form-item__error" v-if="isCheckImg&&!form.imageUrl">
                            请上传主图
                        </div>
                        
                    </el-form-item>






                    <!-- 时间选择器 -->
                    <el-form-item label="生效时间" prop="takeEffectTime">
                        <el-date-picker
                            v-model="form.takeEffectTime"
                            type="datetime"
                            placeholder="选择日期时间">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="失效时间" prop="invalidTime">
                        <el-date-picker
                            v-model="form.invalidTime"
                            type="datetime"
                            placeholder="选择日期时间">
                        </el-date-picker>
                    </el-form-item>

                   <!--  <el-form-item label="排序" prop="nums">
                        <el-input v-model="form.nums"></el-input>
                    </el-form-item> -->

                    <el-form-item label="状态" prop="status">
                        <el-select v-model="form.status" placeholder="请选择状态">
                            <el-option
                                v-for="item in options_statusList"
                                :key="item.typeKey"
                                :label="item.typeValue"
                                :value="item.typeKey">
                            </el-option>
                        </el-select>
                    </el-form-item>

                    
                    <el-form-item>
                        <el-button type="primary" @click="onSubmit">保存</el-button>
                        <el-button @click="cancleSub">取消</el-button>
                    </el-form-item>
                </el-form>

              
              
            </el-dialog>

            <!-- todo -->
            <el-dialog title="预览" 
                ref="imgDialog"
                :visible.sync="imgDialogVisible" 
                :before-close="beforeCloseImg"
                :close-on-click-modal="true"  
                class="abow_dialog yl">
                <iframe :src="zxUrl" frameborder="0" style="min-width: 100%;min-height: 100%;">

                </iframe>
                
            </el-dialog>

            





            
        </div>
    </div>
</template>
<style>
.warning-row>input{
    /* background: red; */
    border: 1px solid red !important;

}

</style>

<script>
    import headTop from '../../components/headTop'
    import {baseUrl, baseImgPath} from '@/config/env'
    import { Loading } from 'element-ui';
    import {uploadImg,queryInformation,addInformation,editInformation,deleteInformation} from '@/api/getData'
    import { common } from '../../api/util'
    // import wangEditor from 'wangeditor'
    export default {
        data(){
            

            let that = this;

            // todo 时间校验  
            let checkAppointTime = (rule, value, callback) => {
                debugger;
                if(!value){
                    callback(new Error('请选择接种日期'));
                }else if(that.form.seckillDatetime >= value){
                    callback(new Error('接种日期请安排在放号日期之后'));
                }else{
                    callback();
                }

            }
            /* let checkTime_takeEffectTime = (rule, value, callback) => {
                debugger;
                if(!value){
                    callback(new Error('请选择生效时间'));
                }
                if(that.form.takeEffectTime && that.form.invalidTime){
                    callback();
                }else if(that.form.takeEffectTime ){

                }else{
                    callback(new Error('请选择接种时间'));
                }
            }; */


            let checkTime_invalidTime = (rule, value, callback) => {
                debugger;
                if(!value){
                    // callback(new Error('请选择失效时间'));
                    callback();
                }else if(that.form.takeEffectTime >= value){
                    callback(new Error('失效时间应该在生效时间之后'));
                }else{
                    callback();
                }
            };

            let checkBody = (rule, value, callback) => {
                debugger;
                
                if(that.form.source === 1 && !that.editor.text()){
                    callback(new Error('请编辑咨询的内容'));
                }else{
                    callback();
                }
            };

            return {
                infoType:"0",
                cancleForm:{
                    userName:"",
                    paperNo:"",
                    vaccineCode:"",
                },
                dateLoading:true,

                fileList:[],
                codeVisible:false,
                // 二级科目列表
                secondList:[],


                hospId:"",
                hospName:"",


                kmId:"",
                departName:"",
                

                

                get pickerOptions(){
                    let that = this;
                    return{
                        disabledDate(time) {
                            // 从明天到后两个月可以放号；
                            // debugger;
                            let date_ = time.format("yyyy-MM-dd");
                            if(time.getTime() < Date.now() ){
                                return true;
                            }else if(time.getTime() > common.getAnotherDay('month', 2, false).getTime()){
                                return true;
                            }else if(that.txList.indexOf(date_)>-1){
                                // 调休需要上班
                                return false;
                            }else if(that.mList.indexOf(date_)>-1 || that.xList.indexOf(date_)>-1){
                                // debugger;
                                return true;
                            }else if(time.getDay() === 0 ||time.getDay() === 6 ){
                                // 周六周日
                                return true;
                            }else{
                                return false;
                            }
                        },
                        cellClassName(time){
                            // debugger;
                            let date_ = time.format("yyyy-MM-dd");
                            if(that.mList.indexOf(date_)>-1){
                                return 'ym';
                            }else if(that.xList.indexOf(date_)>-1){
                                return 'xx';
                            }else if(that.txList.indexOf(date_)>-1){
                                return 'tx';
                            }else if(time.getDay() === 0 ||time.getDay() === 6){
                                return 'xx';
                            }


                        },
                    }
                    
                },
                
                get pickerOptions2(){
                    let that = this;
                    return {
                        disabledDate(time) {
                            // 从明天到后两个月可以放号；
                            // debugger;
                            // let date_ = time.format("yyyy-MM-dd");
                            if(that.form.seckillDatetime){
                                if( time.getTime() > that.form.seckillDatetime.getTime()){
                                    return false;
                                }else{
                                    return true;
                                }
                            }else{
                                return false;
                            }
                            
                        }
                    }
                    
                },

                up_loading:false,
                img_headers:{token:window.localStorage.getItem("token")},

                // 头部搜索条件
                vaccineCode:"",

                baseUrl,
                baseImgPath,
                tableHeight:"",
                mList:[],
                
                // disableList:[],
                

                options_sourceTypeList:[
                    {
                        typeKey:0,
                        typeValue:"链接"
                    },
                    {
                        typeKey:1,
                        typeValue:"非链接"
                    },
                ],
                options_infoTypeList:[],
                options_imageTypeList:[
                    {
                        typeKey:0,
                        typeValue:"无图"
                    },
                    {
                        typeKey:1,
                        typeValue:"小图"
                    },
                    /* {
                        typeKey:2,
                        typeValue:"大图"
                    }, */
                ],

                /* options_sourceTypeList:[
                    {
                        typeKey:0,
                        typeValue:"疫病常识"
                    },
                    {
                        typeKey:1,
                        typeValue:"孕产育儿"
                    },
                ], */

               

                loading:true,
                tableData: [],  


                currentRow: null,
                // offset: 0,
                limit: 10,
                count: 0,
                currentPage: 1,

                


                
                
                appFormVisible: false,
                appFormrules: {
                    title: [
                        { required: true, message: '请输入标题', trigger: ['blur','change'] },
                    ],
                    tips:[{
                        required: true, message: '请输入关键词', trigger: 'change'
                    }],
                    status:[{
                        required: true, message: '请选择状态', trigger: 'change'
                    }],
                    imageType:[{
                        required: true, message: '请选择主图类别', trigger: 'change'
                    }],
                    infoType:[{
                        required: true, message: '请选择所属类别', trigger: 'change'
                    }],
                    source:[{
                        required: true, message: '请选择内容类型', trigger: 'change'
                    }],
                    link:[{
                        required: true, message: '请输入链接地址', trigger: 'change'
                    }],
                    body:[{
                        validator: checkBody, trigger: 'change' 
                    }],
                    
                    nums:[
                        {pattern: /^-?\d+\.?\d*$/,required: true, message: '请输入正确格式的排序', trigger: 'change', }
                    ],

                    takeEffectTime: [
                        {required: true, message: '请选择生效时间', trigger: 'change'}
                    ],

                    invalidTime:[
                        
                        { validator: checkTime_invalidTime, trigger: 'change' }
                    ]
                    
                    // imageUrl:[{
                    //     required: true, message: '请上传应用图标', trigger: 'change'
                    // }],


                },
                
                

                
                form: {
                    title:'',	
                    nums:'',
                    takeEffectTime:'',
                    invalidTime:'',
                    status:'',
                    link:'',	
                    imageUrl:'',
                    imageType:"",
                    infoType:"",
                    body:'',
                    tips:'',
                    source:1,
                },
                imgPath_:"",

                options_statusList :[
                    {
                        typeKey:0,
                        typeValue:"草稿"
                    },
                    {
                        typeKey:1,
                        typeValue:"上线"
                    },
                    {
                        typeKey:2,
                        typeValue:"下线"
                    },

                ],

                isEdit:false,//是否是修改模式
                // isForm:false,  
                isCheckImg:false, //是否需要校验图片有没有上传
                loading_img:true,
                excelName:"",


                title:"",
                status:"",
                timeCondition:[],


                editor:null,
                editorData:"",

                url_:"",
                zxUrl:"",
                imgDialogVisible:false,

            }
        },
        components: {
            headTop,
        },
        created(){
            let that = this;
            this.tableHeight = document.documentElement.clientHeight - 200;
            window.onresize = function(){
              that.tableHeight = document.documentElement.clientHeight - 200;
            }

        },
        mounted(){
            let dic = JSON.parse(window.localStorage.getItem("dataDic"));
            if(dic){
                this.options_infoTypeList = dic.infoTypeList;
            }
            if(this.$route.path==='/ymSys/qxList'){
                this.infoType = "0";
                // this.clearCondition();
            }else if(this.$route.path==='/ymSys/caseList'){
                this.infoType = "1";
                // this.clearCondition();
            }
            this.initData();
        },
        watch:{
            '$route'(){
                debugger;
                // console.log(this.$route.path);
                if(this.$route.path==='/ymSys/qxList'){
                    this.infoType = "0";
                    this.clearCondition();
                }else if(this.$route.path==='/ymSys/caseList'){
                    this.infoType = "1";
                    this.clearCondition();
                }
            }
        },
        methods: {
            beforeCloseImg(done){
                this.zxUrl = "";
                done();
            },
            showZx(row){
                this.zxUrl = row.link;
                this.imgDialogVisible = true;

            },
            rmYY(row){

                // todo取消预约记录  userName paperNo 在查询的时候应该保存起来，不应该从表单里取
                let that = this;
                this.$confirm('确定取消预约?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    customClass:"confirmBox",
                    center: true
                    }).then(() => {
                        cancelReservation(common.formData({
                            orderId:row.recordId
                        }),function(r){
                            that.$message({
                                type: 'success',
                                message: '取消预约成功!'
                            });
                            // todo 刷新列表
                            that.getYYList();
                        });
                    }).catch(() => {
                        this.$message({
                            type: 'info',
                            message: '已取消操作'
                        });
                    });
            },
            dwResult(row){
                // 导出
                let that = this;
                exportByVaccineId(common.formData({
                    vaccineId : row.vaccineId
                }),function(blob){
                        debugger;
                        that.loading = false;
                        var url = window.URL.createObjectURL(blob);
                        var a = document.createElement('a');

                        a.href = url;

                        a.download = "预约记录（"+row.hospName+"-"+row.vaccineName+"-"+row.seckillDate+"）.csv";

                        a.click(); 
                });
            },
            

            getyyStatusTxt(status){
                let obj_ = {
                    1:"预约成功",
                    3:"预约取消"
                };
                return obj_[status] || "未知";

            },
            getStatusTxt(row){
                /* activeStatus 字符串 1生效中/2进行中/3已结束 
                checkStatus 1	未审核
                2	审核通过
                3	审核不通过 
                */
                let checkStatus = {
                    "1":"未审核",	
                    "2":"审核通过",	
                    "3":"审核不通过"	
                };
                let activeStatus = {
                    "1":"生效中",	
                    "2":"放号中",	
                    "3":"已结束"
                };
                if(row.checkStatus === "2"){
                    return activeStatus[row.activeStatus] || "已生效";
                }else{
                    return checkStatus[row.checkStatus] || "状态异常";
                }

            },
            
                
            
            beforeCloseUp(done){
                // 
                this.excelName = "";
                this.$refs.uploadExcel.uploadFiles.length = 0;
                done();
            },
            loadImg(){
                debugger
                this.loading_img = true;
            },
            changeExcel(file){
                
                debugger;
                this.$refs.uploadExcel.uploadFiles.length = 0;
                if(file.status == "success"){
                    return;
                }
                let z = this.beforeAvatarUpload(file.raw);
                if(z){
                    this.excelName = file.name;
                    this.$refs.uploadExcel.uploadFiles.push(file);
                }
                
            },
            beforeAvatarUpload(file){
                // 上传前的校验
                const isRightType = (file.type === 'image/jpeg') || (file.type === 'image/png') || (file.type === (file.type === 'image/jpg'));
                const isLt2M = file.size / 1024 / 1024 < 2;

                if (!isRightType) {
                    this.$message.error('上传图片为jpg/png格式!');
                }
                if (!isLt2M) {
                    this.$message.error('上传图片大小不能超过2MB!');
                }
                return isRightType && isLt2M;

            },
            submitExcel(){
                // this.loading_img = true;
                if(this.$refs.uploadExcel.uploadFiles.length == 0){
                    this.$message.error('请先选择需要上传的文件');
                    return;
                }
                window.loading_excel = this.$loading({
                    lock: true,
                    text: '文件正在导入中，请稍候...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(256, 256, 256, 0.5)'
                });
                this.$refs.uploadExcel.submit();
            },
            handleError(file, fileList) {
                // console.log(file, fileList);
                this.loading_img = false;
                this.$refs.uploadExcel.uploadFiles.length = 0;
                this.excelName = "";
                loading_excel.close();
                this.$message.error('导入失败');
            },
            handleAvatarSuccess(res, file){
                debugger;
                // todo 上传成功
                // this.loading_img = false;
                loading_excel.close();
                this.$refs.uploadExcel.uploadFiles.length = 0;
                this.excelName = "";
                if(res.retCode === "1" || res.respCode===""){
                    this.appFormVisible_ = false;
                    
                    this.$message({
                        type: 'success',
                        message: '导入成功!'
                    });
                    this.clearCondition();
                }else{
                    this.$message.error('导入失败：'+res.respDesc);
                    /* if(res.respCode=="2003" || res.respCode=="2004"){
                        this.$message.error(res.respDesc || '登录超时，即将跳转至登录界面');
                        window.location.href = "#/";
                    } */
                    
                    console.log(this.$refs.uploadExcel.uploadFiles);
                }
            },
            // 二维码
            madeCode(row){
                this.hospName_ = row.hospName;
                this.codeVisible = true;
                this.$nextTick(()=>{
                    if(window.qrcode){
                        window.qrcode.clear();
                        window.qrcode.makeCode(common.hospurl+row.id);

                    }else{
                        window.qrcode = new QRCode(document.getElementById("hospCode"), {
                            text: common.hospurl+row.id,
                            width: 300,
                            height:300,
                            colorDark : "#000000",
                            colorLight : "#ffffff",
                            correctLevel : QRCode.CorrectLevel.H
                        });
                    }
                    

                });
                
                

            },
            
            
           
            
            tableRowClassName(name) {
                debugger;
                if (!name) {
                    return 'warning-row';
                } 
                return '';
            },
            
            
            
            blurF(event){
                if(event.currentTarget.value.trim()){
                    this.updateKm();
                }else{

                }
                
            },
            blurInput(row,index,event){
                debugger;
                this.secondList[index].status = true;
                // this.updateKm();
            },
            
            
           

            
            getTxt_(row,list_,key){
                // let list_txt = "options_"+list_
                debugger
                let list = this[list_];
                let t = "";
                list.forEach(item => {
                    if(row[key] == item.typeKey){
                        t = item.typeValue
                        return;
                    }
                    
                });
                return t ;
                

            },
            getTxt(list,key){
                debugger;
                // let list_txt = "options_"+list_
                // let list = this[list_];
                let t = "";
                list.forEach(item => {
                    if(key == item.optionCode){
                        t = item.optionName
                        return;
                    }
                });
                return t ;
            },
            beforeClose(done){
                debugger;
                this.$refs.form.resetFields();
                this.form.imageUrl = "";
                this.imgPath_  = "" ;
                this.isCheckImg = false;
                done();
            },
            check(name_){
                if(!this.form[name_]){
                    return false;
                }

            },
            resetForm(){
                // this.$refs.form.resetFields();
            },
            search(){
                // 分页1开始
                this.currentPage = 1;
                this.getApps();

            },
            clearCondition(){
                // 重置刷新
                this.title="";
                this.status="";
                this.timeCondition=[];
                // 分页1开始
                this.currentPage = 1;
                this.getApps();

            },
            cancleSub(){

                // this.form = {};
                this.$refs.form.resetFields();
                this.isCheckImg = false;
                this.appFormVisible = false;
                // this.$refs.appDialog.close();
            },
            
            
            
            
            addNew(){
                this.isEdit = false;
                this.isCheckImg = false;
                let that = this;
                this.form = Object.assign({},that.form, {
                    /* appointDate:"",
                    hospTelephone:"",
                    seckillDatetime:"",
                    vaccineCode:"",
                    sourceTotal:"",
                    startTime:"",
                    imageType:"",
                    infoType:"",
                    endTime:"", */
                    title:'',	
                    nums:'',
                    takeEffectTime:'',
                    invalidTime:'',
                    status:'',
                    link:'',	
                    imageUrl:'',
                    imageType:"",
                    infoType:"",
                    body:'',
                    tips:'',
                    source:1,
                });
                this.appFormVisible = true;
                if(this.$refs.form){
                    this.$refs.form.resetFields();
                }
                this.$nextTick(()=>{
                    if(document.querySelector(".el-dialog__body")){
                        document.querySelector(".el-dialog__body").scrollTop = 0;
                    }
                    that.initEditor();
                });
            },

            initEditor(){
                debugger;
                if(!this.editor){
                    this.editor = KindEditor.create('#editor_zx',{
                        // uploadJson : '/console/appStage/uploadPicture',
                        uploadJson : '/yimei/back/information/uploadPicture',
                        // fileManagerJson : '../jsp/file_manager_json.jsp',
                        allowFileManager : false
                    });
                }

                this.editor.html("");
                
                
            },

            edit(row) {
                if(document.querySelector(".el-dialog__body")){
                    document.querySelector(".el-dialog__body").scrollTop = 0;
                }
                this.url_ = row.link;
                // this.form.source = 1;
                
                // this.initEditor();
// 

                debugger;
                this.isEdit = true;
                let that = this;
                this.form = Object.assign({},that.form, row);
                this.imgPath_ = row.imageUrl;
                this.appFormVisible = true;
                // 修改状态 可以不用清除
                // this.$refs.form.resetFields();
                
                // 滚动条复位
                this.$nextTick(()=>{
                    if(document.querySelector(".el-dialog__body")){
                        document.querySelector(".el-dialog__body").scrollTop = 0;
                    }
                    that.initEditor();
                    // todo --测试加头部？？？
                    if(row.body){
                        let z = row.body.split('</title></head><body>')[1];
                        that.editor.html(z.split("</body></html>")[0]);
                    }
                    
                    
                });


            },

            rmApp(row){
                this.$confirm('确定删除该文章?', '提示', {
                    confirmButtonText: '取消',
                    cancelButtonText: '确定',
                    type: 'warning',
                    customClass:"confirmBox",
                    center: true
                    }).then(() => {
                        this.$message({
                            type: 'info',
                            message: '已取消删除'
                        }); 

                    }).catch(() => {
                        // todo 请求删除接口
                        let data = {
                            id:row.id,
                        }
                        let that = this;
                        deleteInformation(data,function(){
                            that.$message({
                                type: 'success',
                                message: '删除成功!'
                            });
                            // todo 刷新列表
                            that.clearCondition();
                        });
                    });
            },
            oprIndex_(index){
                return index+1;
            },
            oprIndex(index){
                // 序号
              let count =(this.currentPage-1)*this.limit+1+index;
              return count;
            },
            submitForm(){
                // this.form.imageUrl = "http://183.136.187.224:8090/appBacImg/test.png";
                debugger;
                let that = this;
                 
                

                let takeEffectTime = this.form.takeEffectTime;
                let invalidTime = this.form.invalidTime ;
                if(takeEffectTime){
                    takeEffectTime = new Date(takeEffectTime).format("yyyy-MM-dd hh:mm:ss");
                }else if(takeEffectTime && typeof(takeEffectTime) == "object"){
                    takeEffectTime.format("yyyy-MM-dd hh:mm:ss");
                } 

                if(invalidTime){
                    invalidTime = new Date(invalidTime).format("yyyy-MM-dd hh:mm:ss");
                }else if(invalidTime && typeof(invalidTime) == "object"){
                    invalidTime = invalidTime.format("yyyy-MM-dd hh:mm:ss");

                } 
                
                debugger;
                let html_ = this.editor.html();
                

                let data = {

                    title:this.form.title,	
                    nums:this.form.nums,
                    takeEffectTime:takeEffectTime,
                    invalidTime:invalidTime,
                    status:this.form.status,
                    link:this.form.link,	
                    imageUrl:this.form.imageUrl,
                    infoType:this.infoType,
                    imageType:this.form.imageType,
                    
                    tips:this.form.tips,
                    source:this.form.source,
                }
                
                
                
                if(that.isEdit){
                    if(this.form.source===1){
                        data.link = this.url_;
                        if(html_.indexOf('<meta')>-1){
                            // 修改头部
                            debugger;
                            let t = html_.split('<title>');
                            let e_ = t[1].split('</title>')[1];
                            html_ = t[0]+'<title>'+this.form.title+'</title>' + e_;
                            

                        }else{
                            html_ =
                            '<!DOCTYPE html><html><head>'+
                                '<meta charset="utf-8">'+
                                '<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no"/>'+
                                '<meta content="yes" name="apple-mobile-web-app-capable"/>'+
                                '<meta content="black" name="apple-mobile-web-app-status-bar-style"/>'+
                                '<meta content="telephone=no,email=no" name="format-detection"/>'+
                                '<link rel="stylesheet" href="https://cdnweb11.96225.com/jkzx/main.css">'+
                                '<title>'+this.form.title+'</title></head><body>' + html_
                            +'</body></html>';
                        }
                        
                    }
                    data.body = html_;
                    data.id = this.form.id;
                    // todo  是否要传修改者的ID
                    editInformation(
                        data ,function(r){
                        // that.$message.success('修改成功');
                        that.appFormVisible = false;
                        that.$message({
                            type: 'success',
                            message: '修改成功!'
                        });
                        // todo 
                        that.form.imageUrl = ""; 
                        that.imgPath_ = "";
                        that.clearCondition();
                    });

                }else{
                    if(this.form.source===1){
                        html_ =
                        '<!DOCTYPE html><html><head>'+
                            '<meta charset="utf-8">'+
                            '<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no"/>'+
                            '<meta content="yes" name="apple-mobile-web-app-capable"/>'+
                            '<meta content="black" name="apple-mobile-web-app-status-bar-style"/>'+
                            '<meta content="telephone=no,email=no" name="format-detection"/>'+
                            '<link rel="stylesheet" href="https://cdnweb11.96225.com/jkzx/main.css">'+
                            '<title>'+this.form.title+'</title></head><body>' + html_
                        +'</body></html>';
                    }
                    data.body = html_;
                    data.creatorId=window.localStorage.getItem("userName_");
                    addInformation(data,function(r){
                        // that.$message.success('新增成功');
                        that.appFormVisible = false;
                        that.$message({
                            type: 'success',
                            message: '新增成功!'
                        });
                        that.clearCondition();
                    });
                }
            },
            imgSuccess(res, file) {
                // 上传图片  file???
                // this.form.imageUrl = URL.createObjectURL(file.raw);
                debugger;
                // if (res.data == 1) {
                    
                //     // 上传表单数据  todo  接口
                // }else{
                //     this.$message.error('上传图片失败！');
                // }
                this.up_loading = false;
                if(res.retCode === "1" || res.respCode===""){
                    this.form.imageUrl = res.value.url;
                    this.submitForm();
                }else{
                    debugger;
                    
                    this.$refs.upload.uploadFiles[0].status = "ready";
                    this.$message.error('上传图片失败！');
                    if(res.respCode=="2003" || res.respCode=="2004"){
                        this.$message.error(res.respDesc || '登录超时，即将跳转至登录界面');
                        window.location.href = "#/";
                    }
                }

                
                // todo test 
                // this.form.imageUrl = "http://183.136.187.224:8090/appBacImg/test.png";
                
                
            },
            imgError(res, file){
                this.up_loading = false;
                this.$refs.upload.uploadFiles[0].status = "ready";
                this.$message.error('上传图片失败！');
            },
            changeImg(file_){
                this.$refs.upload.uploadFiles = [];
                debugger;
                if(file_.status == "success"){
                    return;
                }
                let file = file_.raw;
                // 上传前的校验
                const isRightType = (file.type === 'image/jpeg') || (file.type === 'image/png');
                const isLt2M = file.size / 1024 / 1024 < 2;

                if (!isRightType) {
                    this.$message.error('上传图片为jpg/png格式!');
                    return
                }
                if (!isLt2M) {
                    this.$message.error('上传图片大小不能超过2MB!');
                    return
                }
                console.log("handlePreview");
                
                this.form.imageUrl = "true";
                this.imgPath_ = window.URL.createObjectURL(file);
                // this.imgPath_ = "http://183.136.187.224:8090/appBacImg/test.png";
                // document.querySelector("#img_apps").src=window.URL.createObjectURL(file);
                this.$refs.upload.uploadFiles.push(file_);
                
            },
            /* beforeAvatarUpload(file) {
                // 上传前的校验
                const isRightType = (file.type === 'image/jpeg') || (file.type === 'image/png') || (file.type === (file.type === 'image/jpg'));
                const isLt2M = file.size / 1024 / 1024 < 2;

                if (!isRightType) {
                    this.$message.error('上传图片为jpg/png格式!');
                }
                if (!isLt2M) {
                    this.$message.error('上传图片大小不能超过2MB!');
                }
                return isRightType && isLt2M;
            }, */

            
            // 表单提交
            onSubmit(){
                let result = true;
                this.isCheckImg = true;
                this.$refs.form.validate((valid) => {
                    debugger
                    if (!valid) {
                        result =  false;
                    }
                });
                if(result){ 
                    if(this.form.imageType === 0){
                        // 无图
                        this.form.imageUrl = '';
                        this.submitForm();
                    }else{
                        if(this.form.imageUrl){
                            if(this.form.imageUrl === "true"){
                                // 出现加载中
                                this.up_loading = true;
                                this.$refs.upload.submit();
                            }else{
                                this.submitForm();
                            }
                        }else{
                            // 主图 但没有上传图片
                            this.$nextTick(()=>{
                                document.querySelector(".el-dialog__body").scrollTop =  document.querySelector(".el-form-item__error").parentNode.offsetTop - 40;
                            });
                            return false;
                        }
                    }
                }else{
                    // 没有上传图片
                    debugger;
                    this.$nextTick(()=>{
                        document.querySelector(".el-dialog__body").scrollTop =  document.querySelector(".el-form-item__error").parentNode.offsetTop - 40;
                    });
                    
                    return false;
                }
                
            },
            
            async initData(){
              // 查询应用列表，获去tableData 初始化分页 数据字典
                try{
                    this.getApps();
                }catch(err){
                    console.log('获取数据失败', err);
                }
            },
            handleSizeChange(val) {
                // console.log(`每页 ${val} 条`);
                this.limit = val;
                this.getApps();
            },
            handleCurrentChange(val) {
                this.currentPage = val;
                // this.offset = (val - 1)*this.limit;
                this.getApps()
            },
            getApps(){
             
                let that = this;
                that.loading = true;

                // todo test
                // that.loading = false;
                // return;
                let data = {};

                if(this.timeCondition && this.timeCondition.length >0){
                    data.effectBegin = this.timeCondition[0].format("yyyy-MM-dd");
                    if(this.timeCondition.length == 2){
                        data.effectEnd = this.timeCondition[1].format("yyyy-MM-dd");
                    }

                }
                
                queryInformation(
                    {
                        title:this.title,
                        infoType:this.infoType,
                        status:this.status,
                        effectBegin:data.effectBegin,
                        // vaccineName:this.vaccineName,
                        effectEnd:data.effectEnd,
                        
                        pageNum:this.currentPage,
                        pageSize:this.limit,
                    },
                        

                    
                    function(res){
                        // let data = res.data;
                        let data = res.value;
                        debugger;
                        that.loading = false;
                        that.tableData = [];
                        // 分页
                        that.count = data.total;
                        if(data.list && data.list.length > 0){
                            that.tableData = data.list;
                        }
                        

                        // 设置tableData todo
                        // data.list.forEach(item => {
                        //     const tableData = {};
                        //     tableData.username = item.username;
                        //     tableData.registe_time = item.registe_time;
                        //     tableData.city = item.city;
                        //     that.tableData.push(tableData);
                        // })
                });
                
            }
        },
    }
</script>

<style lang="less">
	
</style>
