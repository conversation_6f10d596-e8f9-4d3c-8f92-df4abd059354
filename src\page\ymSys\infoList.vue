<template>
    <div class="fillcontain">
        <head-top></head-top>
        <div class="table_container">
            <div style="margin-bottom: 20px">
                <el-input
                    v-model="hospName"
                    placeholder="请输入医疗机构名称"
                    style="width: 200px"
                    size="small"
                ></el-input>
                <el-select
                    v-model="districtCode"
                    placeholder="请选择所属城区"
                    style="width: 150px"
                    size="small"
                >
                    <el-option
                        v-for="item in options_districtList"
                        :key="item.optionCode"
                        :label="item.optionName"
                        :value="item.optionCode"
                    >
                    </el-option>
                </el-select>
                <el-select
                    v-model="levelCode"
                    placeholder="请选择机构类别"
                    style="width: 150px"
                    size="small"
                >
                    <el-option
                        v-for="item in options_institutionList"
                        :key="item.optionCode"
                        :label="item.optionName"
                        :value="item.optionCode"
                    >
                    </el-option>
                </el-select>

                <el-button
                    type="primary"
                    style="margin-left: 10px"
                    icon="el-icon-search"
                    size="small"
                    @click="search()"
                    >搜索</el-button
                >
                <!-- <el-button icon="el-icon-search" circle style="margin-left:10px;"></el-button> -->
                <el-button
                    type="warning"
                    size="small"
                    icon="el-icon-refresh"
                    @click="clearCondition()"
                    >重置并刷新</el-button
                >
                <el-button
                    type="primary"
                    style="float: right"
                    icon="el-icon-plus"
                    size="small"
                    @click="addNew()"
                    >新建医院</el-button
                >
                <el-button
                    type="danger"
                    style="float: right"
                    icon="el-icon-plus"
                    size="small"
                    @click="addNew_()"
                    >导入</el-button
                >
            </div>

            <el-table
                size="small"
                :data="tableData"
                v-loading="loading"
                highlight-current-row
                stripe
                border
                :height="tableHeight"
                style="width: 100%"
            >
                <el-table-column
                    type="index"
                    :index="oprIndex"
                    label="序号"
                    width="100"
                >
                </el-table-column>
                <el-table-column
                    property="hospName"
                    label="医疗机构名称"
                    width="220"
                >
                </el-table-column>
                <el-table-column
                    property="districtName"
                    label="区域"
                    width="150"
                >
                </el-table-column>
                <el-table-column property="levelName" label="类别" width="150">
                </el-table-column>
                <el-table-column
                    property="subjectName"
                    label="诊疗项目"
                    width="150"
                >
                </el-table-column>
                <el-table-column property="address" label="地址" width="150">
                </el-table-column>
                <el-table-column property="telephone" label="电话" width="150">
                </el-table-column>
                <el-table-column
                    property="ratingName"
                    label="等级评价"
                    width="150"
                >
                </el-table-column>
                <!-- <el-table-column
                  property=""
                  label="应用状态"
                >
                    <template slot-scope="scope">
                    {{getTxt_(scope.row,"options_statusList","appStatus")}}
                    </template>
                </el-table-column> -->
                <el-table-column
                    property=""
                    label="操作"
                    fixed="right"
                    width="60px"
                >
                    <template slot-scope="scope">
                        <!-- <el-button @click="edit(scope.row)" type="text" size="small">修改</el-button> -->
                        <el-button
                            type="primary"
                            icon="el-icon-edit"
                            circle
                            @click="edit(scope.row)"
                            size="small"
                        ></el-button>
                        <!-- <el-button type="danger" icon="el-icon-delete" circle size="small" @click="rmApp(scope.row)"></el-button> -->
                        <!-- <el-button type="text" size="small" >删除</el-button> -->
                    </template>
                </el-table-column>

                <el-table-column label="诊疗科目" fixed="right">
                    <template slot-scope="scope">
                        <el-button
                            type="primary"
                            plain
                            @click="editDepart(scope.row)"
                            size="small"
                            >详情</el-button
                        >
                    </template>
                </el-table-column>

                <el-table-column label="医生" fixed="right">
                    <template slot-scope="scope">
                        <el-button
                            type="primary"
                            plain
                            @click="editDoc(scope.row)"
                            size="small"
                            >详情</el-button
                        >
                    </template>
                </el-table-column>
                <el-table-column fixed="right" label="二维码">
                    <template slot-scope="scope">
                        <el-button
                            type="warning"
                            @click="madeCode(scope.row)"
                            size="small"
                            >生成</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>

            <div class="Pagination" style="text-align: left; margin-top: 10px">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 20, 30, 40]"
                    :page-size="10"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="count"
                >
                </el-pagination>
            </div>
            <el-dialog
                :title="(isEdit ? '修改' : '新增') + '医院'"
                ref="appDialog"
                :visible.sync="appFormVisible"
                :close-on-click-modal="false"
                :before-close="beforeClose"
                v-loading="up_loading"
                element-loading-background="rgba(0, 0, 0, 0.3)"
                class="abow_dialog"
            >
                <el-form
                    ref="form"
                    :model="form"
                    label-width="120px"
                    :rules="appFormrules"
                    :validate-on-rule-change="false"
                >
                    <el-form-item label="医疗机构名称" prop="hospName">
                        <el-input v-model="form.hospName"></el-input>
                    </el-form-item>

                    <el-form-item label="所属城区" prop="districtCode">
                        <el-select
                            v-model="form.districtCode"
                            placeholder="请选择所属城区"
                        >
                            <el-option
                                v-for="item in options_districtList"
                                :key="item.optionCode"
                                :label="item.optionName"
                                :value="item.optionCode"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="类别" prop="levelCode">
                        <el-select
                            v-model="form.levelCode"
                            placeholder="请选择类别"
                        >
                            <el-option
                                v-for="item in options_institutionList"
                                :key="item.optionCode"
                                :label="item.optionName"
                                :value="item.optionCode"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="诊疗项目" prop="subjectCode">
                        <el-select
                            v-model="form.subjectCode"
                            placeholder="请选择诊疗项目"
                        >
                            <el-option
                                v-for="item in options_subjectsList"
                                :key="item.optionCode"
                                :label="item.optionName"
                                :value="item.optionCode"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="等级评价" prop="ratingCode">
                        <el-select
                            v-model="form.ratingCode"
                            placeholder="请选择等级评价"
                        >
                            <el-option
                                v-for="item in options_level"
                                :key="item.optionCode"
                                :label="item.optionName"
                                :value="item.optionCode"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="地址" prop="address">
                        <el-input v-model="form.address"></el-input>
                    </el-form-item>
                    <el-form-item label="电话" prop="telephone">
                        <el-input v-model="form.telephone"></el-input>
                    </el-form-item>

                    <!-- 时间选择器  todo是否为范围？？？？ -->
                    <el-form-item label="许可证到期日" prop="expirationDate">
                        <el-date-picker
                            v-model="form.expirationDate"
                            type="date"
                            placeholder="选择日期时间"
                        >
                        </el-date-picker>
                    </el-form-item>

                    <el-form-item label="医疗机构信息" prop="hospInfo">
                        <el-input
                            type="textarea"
                            v-model="form.hospInfo"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="备案情况" prop="recordInfo">
                        <el-input
                            type="textarea"
                            v-model="form.recordInfo"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="行政处罚" prop="punishmentInfo">
                        <el-input
                            type="textarea"
                            v-model="form.punishmentInfo"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="收费情况">
                        <el-input
                            type="textarea"
                            v-model="form.feeInfo"
                            placeholder="详情请联系该机构了解"
                        ></el-input>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="onSubmit"
                            >保存</el-button
                        >
                        <el-button @click="cancleSub">取消</el-button>
                    </el-form-item>
                </el-form>
            </el-dialog>

            <el-dialog
                :title="hospName_ + '二维码'"
                :visible.sync="codeVisible"
                class="abow_dialog"
            >
                <el-button
                    type="primary"
                    @click="downloadCode"
                    icon="el-icon-download"
                    style="margin-top: 0px; margin-bottom: 10px"
                    >保存二维码</el-button
                >
                <div id="hospCode"></div>
            </el-dialog>

            <el-dialog
                title="诊疗科目"
                :visible.sync="ztSettingsVisible"
                class="abow_dialog fullScreen_dialog"
            >
                <el-button
                    type="primary"
                    style="float: right; margin-bottom: 20px"
                    icon="el-icon-plus"
                    size="small"
                    @click="addKm()"
                    >添加科目</el-button
                >
                <el-table
                    size="small"
                    :data="appsTable"
                    highlight-current-row
                    stripe
                    border
                    :height="tableHeight"
                    v-loading="loading_app"
                    style="width: 100%"
                >
                    <el-table-column
                        type="index"
                        :index="oprIndex_"
                        label="序号"
                        width="100"
                    >
                    </el-table-column>

                    <el-table-column property="departName" label="一级科目">
                    </el-table-column>

                    <el-table-column property="" label="操作" fixed="right">
                        <template slot-scope="scope">
                            <el-button
                                type="primary"
                                icon="el-icon-edit"
                                circle
                                @click="editKm(scope.row)"
                                size="small"
                            ></el-button>
                            <el-button
                                type="danger"
                                icon="el-icon-delete"
                                circle
                                size="small"
                                @click="rmKm(scope.row)"
                            ></el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-dialog
                    :title="(isEditKm ? '修改' : '新增') + '科目'"
                    ref="appDialog"
                    :visible.sync="appDialogVisible"
                    :close-on-click-modal="false"
                    :before-close="beforeCloseKm"
                    append-to-body
                    class="abow_dialog"
                >
                    <el-form
                        ref="kmform"
                        label-width="80px"
                        :rules="kmFormrules"
                        :model="kmform"
                        :validate-on-rule-change="false"
                    >
                        <el-form-item label="一级科目" prop="departName">
                            <el-input
                                v-model="kmform.departName"
                                placeholder="请输入一级科目"
                            ></el-input>
                        </el-form-item>
                        <!-- <el-button type="primary" style="float:right;margin-bottom:20px;" icon="el-icon-plus" 
                        size="small" @click="addNewSecond()">添加二级科目</el-button> -->

                        <el-table
                            size="small"
                            :data="secondList"
                            highlight-current-row
                            stripe
                            border
                            v-loading="loading_app"
                            style="width: 100%"
                        >
                            <el-table-column
                                type="index"
                                :index="oprIndex_"
                                label="序号"
                                width="100"
                            >
                            </el-table-column>

                            <el-table-column property="" label="二级科目">
                                <template slot-scope="scope">
                                    <el-input
                                        :ref="'input' + scope.$index"
                                        placeholder="请输入二级科目"
                                        :class="
                                            tableRowClassName(scope.row.name)
                                        "
                                        v-model="scope.row.name"
                                        @blur="
                                            blurInput(scope.row, scope.$index)
                                        "
                                        :disabled="scope.row.status"
                                        clearable
                                    >
                                    </el-input>
                                </template>
                            </el-table-column>

                            <el-table-column
                                property=""
                                label="操作"
                                fixed="right"
                            >
                                <template slot-scope="scope">
                                    <el-button
                                        type="primary"
                                        icon="el-icon-edit"
                                        circle
                                        @click="
                                            editKmS(scope.row, scope.$index)
                                        "
                                        size="small"
                                    ></el-button>
                                    <el-button
                                        type="danger"
                                        icon="el-icon-delete"
                                        circle
                                        size="small"
                                        @click="rmKmS(scope.row, scope.$index)"
                                    ></el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <el-form-item style="margin-top: 10px">
                            <el-button
                                type="primary"
                                style="float: right; margin-bottom: 20px"
                                icon="el-icon-plus"
                                size="small"
                                @click="addNewSecond()"
                                >添加二级科目</el-button
                            >
                        </el-form-item>

                        <el-form-item style="margin-top: 10px">
                            <el-button type="primary" @click="onSubmitKm"
                                >保存</el-button
                            >
                            <el-button @click="cancleSubKm">取消</el-button>
                        </el-form-item>
                    </el-form>
                </el-dialog>
            </el-dialog>

            <el-dialog
                title="医生"
                :visible.sync="docsVisible"
                class="abow_dialog fullScreen_dialog"
            >
                <el-button
                    type="primary"
                    style="float: right; margin-bottom: 20px"
                    icon="el-icon-plus"
                    size="small"
                    @click="addDocB()"
                    >添加医生</el-button
                >
                <el-table
                    size="small"
                    :data="docsTable"
                    highlight-current-row
                    stripe
                    border
                    v-loading="loading_doc"
                    style="width: 100%"
                >
                    <el-table-column
                        type="index"
                        :index="oprIndex_"
                        label="序号"
                        width="100"
                    >
                    </el-table-column>

                    <el-table-column property="doctorName" label="医生姓名">
                    </el-table-column>

                    <el-table-column property="" label="操作" fixed="right">
                        <template slot-scope="scope">
                            <el-button
                                type="primary"
                                icon="el-icon-edit"
                                circle
                                @click="editDocB(scope.row)"
                                size="small"
                            ></el-button>
                            <el-button
                                type="danger"
                                icon="el-icon-delete"
                                circle
                                size="small"
                                @click="rmDocB(scope.row)"
                            ></el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-dialog
                    :title="(isEditDoc ? '修改' : '新增') + '医生'"
                    ref="appDialog"
                    :visible.sync="docInfoVisible"
                    :close-on-click-modal="false"
                    :before-close="beforeCloseDoc"
                    append-to-body
                    class="abow_dialog"
                >
                    <el-form
                        ref="docForm"
                        label-width="120px"
                        :rules="docFormrules"
                        :model="docForm"
                        :validate-on-rule-change="false"
                    >
                        <el-form-item label="医生姓名" prop="doctorName">
                            <el-input
                                v-model="docForm.doctorName"
                                placeholder="请输入医生姓名"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="是否主任医师" prop="attendingFlag">
                            <el-select
                                v-model="docForm.attendingFlag"
                                placeholder="请选择所属城区"
                            >
                                <el-option
                                    v-for="item in options_yesNoList"
                                    :key="item.optionCode"
                                    :label="item.optionName"
                                    :value="item.optionCode"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item label="执业范围" prop="scopeCode">
                            <el-select
                                v-model="docForm.scopeCode"
                                placeholder="请选择所属城区"
                            >
                                <el-option
                                    v-for="item in options_scopeList"
                                    :key="item.optionCode"
                                    :label="item.optionName"
                                    :value="item.optionCode"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item label="医生介绍" prop="profile">
                            <el-input
                                v-model="docForm.profile"
                                placeholder="请输入医生介绍"
                                type="textarea"
                            ></el-input>
                        </el-form-item>

                        <el-form-item style="margin-top: 10px">
                            <el-button type="primary" @click="onSubmitDoc"
                                >保存</el-button
                            >
                            <el-button @click="cancleSubDoc">取消</el-button>
                        </el-form-item>
                    </el-form>
                </el-dialog>
            </el-dialog>

            <el-dialog
                title="导入"
                ref="appDialog_"
                :visible.sync="appFormVisible_"
                :close-on-click-modal="false"
                :before-close="beforeCloseUp"
                element-loading-background="rgba(0, 0, 0, 0.3)"
                class="abow_dialog"
            >
                <p>
                    请上传按照摸个格式填写的excel文档
                    <a
                        href="http://183.136.187.224:8011/ymManage/static/ymcc.xlsx"
                        target="_blank"
                        download="模板.xls"
                        ><i class="el-icon-download"></i>excel模板下载</a
                    >
                    <br /><br />
                </p>

                <div>
                    <el-upload
                        class="avatar-uploader"
                        :headers="img_headers"
                        :before-upload="beforeAvatarUpload"
                        action="/yimei/file/loadExcel"
                        :show-file-list="false"
                        :auto-upload="false"
                        ref="uploadExcel"
                        :on-error="handleError"
                        :on-change="changeExcel"
                        :on-success="handleAvatarSuccess"
                        :file-list="fileList"
                    >
                        <i class="el-icon-plus avatar-uploader-icon"></i>

                        <div
                            slot="tip"
                            class="el-upload__tip danger"
                            style="color: #f56c6c"
                        >
                            只能上传excel文件
                        </div>

                        <!-- <img v-if="imageUrl" :src="imageUrl" class="avatar"> -->
                        <!-- <i v-else class="el-icon-plus avatar-uploader-icon"></i> -->
                    </el-upload>

                    <p v-show="excelName" style="margin: 10px 0px 、0px">
                        需上传的文件：{{ excelName }}
                    </p>

                    <el-button
                        size="middle"
                        style="margin: 10px 0px 10px"
                        type="success"
                        @click="submitExcel"
                        >上传导入</el-button
                    >
                    <!-- <input type="file" id="file" multiple="multiple" onchange="handleFile()"/> -->
                </div>
            </el-dialog>
        </div>
    </div>
</template>
<style>
.warning-row > input {
    /* background: red; */
    border: 1px solid red !important;
}
</style>

<script>
import headTop from "../../components/headTop";
import { baseUrl, baseImgPath } from "@/config/env";
import { Loading } from "element-ui";
import {
    getAppList,
    insertApplicationData,
    updateApplicationData,
    getKmList,
    updateKmList,
    addKmList,
    deleteKmList,
    getDocList,
    addDoc,
    updateDoc,
    deleDoc,
} from "@/api/getData";
import { common } from "../../api/util";
// import {mapDic} from 'vuex'
export default {
    data() {
        let that = this;
        let checkTime = (rule, value, callback) => {
            debugger;
            if (that.form.timeType == "短期" && !value) {
                callback(new Error("请选择时间"));
            } else {
                callback();
            }
        };
        return {
            fileList: [],
            codeVisible: false,
            // 二级科目列表
            secondList: [],

            hospId: "",
            kmId: "",
            departName: "",
            options_districtList: [],
            options_gradeList: [],
            options_subjectsList: [],
            options_scopeList: [],
            options_institutionList: [],
            options_yesNoList: [],
            options_level: [],

            pickerOptions2: {
                /* shortcuts: [{
                        text: '最近一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近三个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }] */
            },

            up_loading: false,
            img_headers: { token: window.localStorage.getItem("token") },

            // 头部搜索条件
            hospName: "",
            levelCode: "",
            districtCode: "",

            baseUrl,
            baseImgPath,
            tableHeight: "",
            options: [
                {
                    typeKey: "0",
                    typeValue: "原生",
                },
                {
                    typeKey: "1",
                    typeValue: "原生2",
                },
            ],
            loading: true,
            tableData: [],

            currentRow: null,
            // offset: 0,
            limit: 10,
            count: 0,
            currentPage: 1,

            // 医生
            docsVisible: false,
            docsTable: [],
            loading_doc: true,

            docInfoVisible: false,
            isEditDoc: false,

            // 诊疗科目弹出层
            ztSettingsVisible: false,
            //诊疗科目列表
            appsTable: [],
            loading_app: true,
            appDialogVisible: false,
            isEditApp: false,
            isEditKm: false,
            kmform: { departName: "" },
            kmFormrules: {
                departName: [
                    {
                        required: true,
                        message: "请输入一级科目",
                        trigger: ["blur", "change"],
                    },
                ],
            },
            docFormrules: {
                doctorName: [
                    {
                        required: true,
                        message: "请输入医生姓名",
                        trigger: ["blur", "change"],
                    },
                ],
                scopeCode: [
                    {
                        required: true,
                        message: "请选择执业范围",
                        trigger: ["change"],
                    },
                ],
                attendingFlag: [
                    { required: true, message: "请选择", trigger: ["change"] },
                ],
            },
            appFormVisible: false,
            appFormrules: {
                hospName: [
                    {
                        required: true,
                        message: "请输入医院名称",
                        trigger: ["blur", "change"],
                    },
                ],
                levelCode: [
                    {
                        required: true,
                        message: "请选择类别",
                        trigger: "change",
                    },
                ],
                districtCode: [
                    {
                        required: true,
                        message: "请选择区域",
                        trigger: "change",
                    },
                ],
                address: [
                    {
                        required: true,
                        message: "请输入地址",
                        trigger: "change",
                    },
                ],
                telephone: [
                    {
                        required: true,
                        message: "请输入电话号码",
                        trigger: "change",
                    },
                ],

                ratingCode: [
                    {
                        required: true,
                        message: "请选择等级评价",
                        trigger: "change",
                    },
                ],
                hospInfo: [
                    {
                        required: true,
                        message: "请填写医疗机构信息",
                        trigger: "change",
                    },
                ],
                recordInfo: [
                    {
                        required: true,
                        message: "请填写备案情况",
                        trigger: "change",
                    },
                ],
                punishmentInfo: [
                    {
                        required: true,
                        message: "请填写行政处罚",
                        trigger: "change",
                    },
                ],
                feeInfo: [
                    {
                        required: true,
                        message: "请填写收费情况",
                        trigger: "change",
                    },
                ],
                expirationDate: [
                    {
                        required: true,
                        message: "请选择有效期",
                        trigger: "change",
                    },
                ],
                subjectCode: [
                    {
                        required: true,
                        message: "请选择诊疗项目",
                        trigger: "change",
                    },
                ],

                /* takeEffectTime: [
                        { validator: checkTime, trigger: 'change' }
                    ], */

                // iconUrl:[{
                //     required: true, message: '请上传应用图标', trigger: 'change'
                // }],
            },
            appFormVisible_: false,

            docForm: {
                doctorName: "",
                scopeCode: "",
                scopeName: "",
                hospId: "",
                profile: "",
                attendingFlag: "",
            },
            hospName_: "",
            form: {
                hospName: "",
                levelCode: "",
                levelName: "",
                districtCode: "",
                districtName: "",
                address: "",
                telephone: "",
                ratingCode: "",
                ratingName: "",
                hospInfo: "",
                recordInfo: "",
                punishmentInfo: "",
                feeInfo: "",
                expirationDate: "",
                subjectCode: "",
                subjectName: "",
            },
            imgPath_: "",

            options_statusList: [],
            options_appTypeList: [],
            options_authStatusList: [],

            docId: "",

            isEdit: false, //是否是修改模式
            // isForm:false,
            isCheckImg: false, //是否需要校验图片有没有上传
            loading_img: true,
            excelName: "",
        };
    },
    components: {
        headTop,
    },
    created() {
        let that = this;
        this.tableHeight = document.documentElement.clientHeight - 200;
        window.onresize = function () {
            that.tableHeight = document.documentElement.clientHeight - 200;
        };
    },
    mounted() {
        this.initData();
        let dic = JSON.parse(window.localStorage.getItem("dataDic"));
        debugger;
        // 应用状态 statusList 应用类别appTypeList 应用权限 authStatusList
        // this.options_statusList = this.$store.state.dic.list.statusList;
        // this.options_appTypeList =  this.$store.state.dic.list.appTypeList;
        // this.options_authStatusList =  this.$store.state.dic.list.authStatusList;
        if (dic) {
            this.options_districtList = dic.district;
            this.options_gradeList = dic.grade;
            this.options_subjectsList = dic.subjects;
            this.options_scopeList = dic.scope;
            this.options_institutionList = dic.institution;
            this.options_yesNoList = dic.yesNo;
            this.options_level = dic.level;
        }

        // this.$store.state.dic.list;
    },
    methods: {
        beforeCloseUp(done) {
            //
            this.excelName = "";
            this.$refs.uploadExcel.uploadFiles.length = 0;
            done();
        },
        loadImg() {
            debugger;
            this.loading_img = true;
        },
        changeExcel(file) {
            debugger;
            this.$refs.uploadExcel.uploadFiles.length = 0;
            if (file.status == "success") {
                return;
            }
            let z = this.beforeAvatarUpload(file.raw);
            if (z) {
                this.excelName = file.name;
                this.$refs.uploadExcel.uploadFiles.push(file);
            }
        },
        beforeAvatarUpload(file) {
            debugger;
            const isExcle =
                file.type ===
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
                file.type === "application/vnd.ms-excel" ||
                file.type === "application/x-excel" ||
                file.type === "application/vnd.ms-excel application/x-excel";
            // const isLt2M = file.size / 1024 / 1024 < 2;
            if (!isExcle) {
                this.$message.error("上传文件格式不正确");
            }
            return isExcle;
        },
        submitExcel() {
            // this.loading_img = true;
            if (this.$refs.uploadExcel.uploadFiles.length == 0) {
                this.$message.error("请先选择需要上传的文件");
                return;
            }
            window.loading_excel = this.$loading({
                lock: true,
                text: "文件正在导入中，请稍候...",
                spinner: "el-icon-loading",
                background: "rgba(256, 256, 256, 0.5)",
            });
            this.$refs.uploadExcel.submit();
        },
        handleError(file, fileList) {
            // console.log(file, fileList);
            this.loading_img = false;
            this.$refs.uploadExcel.uploadFiles.length = 0;
            this.excelName = "";
            loading_excel.close();
            this.$message.error("导入失败");
        },
        handleAvatarSuccess(res, file) {
            debugger;
            // todo 上传成功
            // this.loading_img = false;
            loading_excel.close();
            this.$refs.uploadExcel.uploadFiles.length = 0;
            this.excelName = "";
            if (res.retCode === "1" || res.respCode === "") {
                this.appFormVisible_ = false;

                this.$message({
                    type: "success",
                    message: "导入成功!",
                });
                this.clearCondition();
            } else {
                this.$message.error("导入失败：" + res.respDesc);
                /* if(res.respCode=="2003" || res.respCode=="2004"){
                        this.$message.error(res.respDesc || '登录超时，即将跳转至登录界面');
                        window.location.href = "#/";
                    } */

                console.log(this.$refs.uploadExcel.uploadFiles);
            }
        },
        // 二维码
        madeCode(row) {
            this.hospName_ = row.hospName;
            this.codeVisible = true;
            this.$nextTick(() => {
                if (window.qrcode) {
                    window.qrcode.clear();
                    window.qrcode.makeCode(common.hospurl + row.id);
                } else {
                    window.qrcode = new QRCode(
                        document.getElementById("hospCode"),
                        {
                            text: common.hospurl + row.id,
                            width: 300,
                            height: 300,
                            colorDark: "#000000",
                            colorLight: "#ffffff",
                            correctLevel: QRCode.CorrectLevel.H,
                        }
                    );
                }
            });
        },
        downloadCode() {
            var link = document.createElement("a");
            var url = $("#hospCode").find("img")[0].src;
            link.setAttribute("href", url);
            link.setAttribute("download", this.hospName_ + ".png");
            link.click();
        },
        addDocB() {
            this.docForm = Object.assign({}, this.docForm, {
                doctorName: "",
                scopeCode: "",
                scopeName: "",
                hospId: "",
                profile: "",
                attendingFlag: "",
            });
            this.isEditDoc = false;
            this.docInfoVisible = true;
            // this.$refs.docForm.resetFields();
            this.$nextTick(() => {
                this.$refs.docForm.resetFields();
            });
        },
        editDocB(row) {
            this.docForm = Object.assign({}, this.docForm, row);
            this.isEditDoc = true;
            this.docId = row.id;
            this.docInfoVisible = true;
        },
        rmDocB(row) {
            debugger;
            this.$confirm("确定删除?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
                customClass: "confirmBox",
                center: true,
            })
                .then(() => {
                    // todo 请求删除接口
                    let data = {
                        id: row.id,
                    };
                    let that = this;
                    deleDoc(data, function () {
                        that.$message({
                            type: "success",
                            message: "删除成功!",
                        });
                        // todo 刷新列表
                        that.getDocs();
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除",
                    });
                });
        },
        addDocInfo() {
            let that = this;
            // levelName:this.getTxt(this.options_institutionList,this.form.levelCode),
            addDoc(
                {
                    doctorName: this.docForm.doctorName,
                    scopeCode: this.docForm.scopeCode,
                    scopeName: this.getTxt(
                        this.options_scopeList,
                        this.docForm.scopeCode
                    ),
                    hospId: this.hospId,
                    profile: this.docForm.profile,
                    attendingFlag: this.docForm.attendingFlag,
                },
                function (r) {
                    that.$message({
                        type: "success",
                        message: "添加成功!",
                    });
                    that.docInfoVisible = false;
                    that.getDocs();
                }
            );
        },
        updateDocInfo() {
            let that = this;
            updateDoc(
                {
                    id: this.docId,
                    doctorName: this.docForm.doctorName,
                    scopeCode: this.docForm.scopeCode,
                    scopeName: this.getTxt(
                        this.options_scopeList,
                        this.docForm.scopeCode
                    ),
                    hospId: this.hospId,
                    profile: this.docForm.profile,
                    attendingFlag: this.docForm.attendingFlag,
                },
                function (r) {
                    that.$message({
                        type: "success",
                        message: "修改成功!",
                    });
                    that.docInfoVisible = false;
                    that.getDocs();
                }
            );
        },
        onSubmitDoc() {
            let result = true;
            this.$refs.docForm.validate((valid) => {
                if (!valid) {
                    result = false;
                }
            });
            if (result) {
                // 检查二级科目
                if (this.isEditDoc) {
                    this.updateDocInfo();
                } else {
                    this.addDocInfo();
                }
            } else {
                return false;
            }
        },
        cancleSubDoc() {
            this.$refs.docForm.resetFields();
            this.docInfoVisible = false;
        },
        addNewSecond() {
            this.secondList.push({ name: "", status: false });
        },
        tableRowClassName(name) {
            debugger;
            if (!name) {
                return "warning-row";
            }
            return "";
        },
        beforeCloseKm(done) {
            this.$refs.kmform.resetFields();
            // this.getList();
            done();
        },
        beforeCloseDoc(done) {
            this.$refs.docForm.resetFields();
            // this.getList();
            done();
        },
        addKmL() {
            // 更新科目
            let list = [];
            this.secondList.forEach((it) => {
                list.push(it.name);
            });
            let that = this;
            addKmList(
                {
                    hospId: that.hospId,
                    // id:that.kmId,
                    departName: that.kmform.departName,
                    subDepartObj: list,
                },
                function (r) {
                    that.$message({
                        type: "success",
                        message: "添加成功!",
                    });
                    that.getList();
                }
            );
        },
        updateKm() {
            // 更新科目
            let list = [];
            this.secondList.forEach((it) => {
                list.push(it.name);
            });
            let that = this;
            updateKmList(
                {
                    hospId: that.hospId,
                    id: that.kmId,
                    departName: that.kmform.departName,
                    subDepartObj: list,
                },
                function (r) {
                    that.$message({
                        type: "success",
                        message: "修改成功!",
                    });
                    that.getList();
                }
            );
        },
        blurF(event) {
            if (event.currentTarget.value.trim()) {
                this.updateKm();
            } else {
            }
        },
        blurInput(row, index, event) {
            debugger;
            this.secondList[index].status = true;
            // this.updateKm();
        },
        addKm() {
            this.kmId = "";
            this.departName = "";
            this.kmform.departName = "";
            this.isEditKm = false;

            this.appDialogVisible = true;
            this.secondList = [];
            this.$nextTick(() => {
                this.$refs.kmform.resetFields();
            });
        },
        editKmS(row, index) {
            debugger;
            this.secondList[index].status = false;
            // this.$refs["input"+index].readonly = false;
        },
        editKm(row) {
            debugger;
            this.kmId = row.id;
            this.departName = row.departName;
            this.kmform.departName = row.departName;
            this.appDialogVisible = true;
            this.isEditKm = true;
            // this.getSecondList();
            this.secondList = [];
            row.subDepartObj.forEach((it) => {
                this.secondList.push({ name: it, status: true });
            });
            // this.secondList = row.subDepartObj;
        },
        rmKmS(row, index) {
            this.secondList.splice(index, 1);
        },
        rmKm(row) {
            // 删除科目
            this.$confirm("确定删除?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
                customClass: "confirmBox",
                center: true,
            })
                .then(() => {
                    // todo 请求删除接口
                    let data = {
                        id: row.id,
                    };
                    let that = this;
                    deleteKmList(data, function () {
                        that.$message({
                            type: "success",
                            message: "删除成功!",
                        });
                        // todo 刷新列表
                        that.getList();
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除",
                    });
                });
        },

        // 诊疗科目修改、查看
        editDepart(row) {
            // todo  获取下面的应用列表
            this.hospId = row.id;
            this.ztSettingsVisible = true;
            this.getList();
        },

        // 医生查看、修改
        editDoc(row) {
            // todo  获取下面的应用列表
            this.hospId = row.id;
            this.docsVisible = true;
            this.getDocs();
        },

        // 获取诊疗科目
        getList() {
            let that = this;
            that.loading_app = true;
            getKmList({ hospId: that.hospId }, function (r) {
                that.appsTable = r.value;
                that.loading_app = false;
            });
        },
        getDocs() {
            let that = this;
            that.loading_doc = true;
            getDocList({ hospId: that.hospId }, function (r) {
                that.docsTable = r.value;
                that.loading_doc = false;
            });
        },
        getTxt_(row, list_, key) {
            // let list_txt = "options_"+list_
            let list = this[list_];
            let t = "";
            list.forEach((item) => {
                if (row[key] == item.optionCode) {
                    t = item.optionName;
                    return;
                }
            });
            return t;
        },
        getTxt(list, key) {
            debugger;
            // let list_txt = "options_"+list_
            // let list = this[list_];
            let t = "";
            list.forEach((item) => {
                if (key == item.optionCode) {
                    t = item.optionName;
                    return;
                }
            });
            return t;
        },
        beforeClose(done) {
            debugger;
            this.$refs.form.resetFields();
            this.form.iconUrl = "";
            this.imgPath_ = "";
            this.isCheckImg = false;
            done();
        },
        check(name_) {
            if (!this.form[name_]) {
                return false;
            }
        },
        resetForm() {
            // this.$refs.form.resetFields();
        },
        search() {
            // 分页1开始
            this.currentPage = 1;
            this.getApps();
        },
        clearCondition() {
            // 重置刷新

            (this.hospName = ""),
                (this.levelCode = ""),
                (this.districtCode = "");
            // 分页1开始
            this.currentPage = 1;

            this.getApps();
        },
        cancleSub() {
            // this.form = {};
            this.$refs.form.resetFields();
            this.isCheckImg = false;
            this.appFormVisible = false;
            // this.$refs.appDialog.close();
        },
        addNew_() {
            this.appFormVisible_ = true;
        },
        addNew() {
            this.isEdit = false;
            this.isCheckImg = false;
            let that = this;
            this.form = Object.assign({}, that.form, {
                hospName: "",
                levelCode: "",
                levelName: "",
                districtCode: "",
                districtName: "",
                address: "",
                telephone: "",
                ratingCode: "",
                ratingName: "",
                hospInfo: "",
                recordInfo: "",
                punishmentInfo: "",
                feeInfo: "",
                expirationDate: "",
                subjectCode: "",
                subjectName: "",
            });
            this.appFormVisible = true;
            if (this.$refs.form) {
                this.$refs.form.resetFields();
            }
            this.$nextTick(() => {
                if (document.querySelector(".el-dialog__body")) {
                    document.querySelector(".el-dialog__body").scrollTop = 0;
                }
            });
        },

        edit(row) {
            if (document.querySelector(".el-dialog__body")) {
                document.querySelector(".el-dialog__body").scrollTop = 0;
            }

            this.isEdit = true;
            let that = this;
            this.form = Object.assign({}, that.form, row);
            // 修改状态 可以不用清除
            // this.$refs.form.resetFields();
            this.appFormVisible = true;
            // 滚动条复位
            this.$nextTick(() => {
                if (document.querySelector(".el-dialog__body")) {
                    document.querySelector(".el-dialog__body").scrollTop = 0;
                }
            });
        },

        rmApp(row) {
            this.$confirm("确定删除该应用?", "提示", {
                confirmButtonText: "取消",
                cancelButtonText: "确定",
                type: "warning",
                customClass: "confirmBox",
                center: true,
            })
                .then(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除",
                    });
                })
                .catch(() => {
                    // todo 请求删除接口
                    let data = {
                        applicationId: row.applicationId,
                        applicationName: row.applicationName,
                    };
                    let that = this;
                    deleteApplicationData(data, function () {
                        that.$message({
                            type: "success",
                            message: "删除成功!",
                        });
                        // todo 刷新列表
                        that.clearCondition();
                    });
                });
        },
        oprIndex_(index) {
            return index + 1;
        },
        oprIndex(index) {
            // 序号
            let count = (this.currentPage - 1) * this.limit + 1 + index;
            return count;
        },
        submitForm() {
            // this.form.iconUrl = "http://183.136.187.224:8090/appBacImg/test.png";
            this.form.expirationDate;
            debugger;
            let data = {
                hospName: this.form.hospName,
                levelCode: this.form.levelCode,
                levelName: this.getTxt(
                    this.options_institutionList,
                    this.form.levelCode
                ),
                districtCode: this.form.districtCode,
                districtName: this.getTxt(
                    this.options_districtList,
                    this.form.districtCode
                ),
                address: this.form.address,
                telephone: this.form.telephone,
                ratingCode: this.form.ratingCode,
                ratingName: this.getTxt(
                    this.options_level,
                    this.form.ratingCode
                ),
                hospInfo: this.form.hospInfo,
                recordInfo: this.form.recordInfo,
                punishmentInfo: this.form.punishmentInfo,
                feeInfo: this.form.feeInfo || "详情请联系该机构了解",
                expirationDate: new Date(this.form.expirationDate).format(
                    "yyyy-MM-dd"
                ),
                subjectCode: this.form.subjectCode,
                subjectName: this.getTxt(
                    this.options_subjectsList,
                    this.form.subjectCode
                ),
            };
            let that = this;

            if (that.isEdit) {
                data.id = this.form.id;
                // todo  是否要传修改者的ID
                updateApplicationData(data, function (r) {
                    // that.$message.success('修改成功');
                    that.appFormVisible = false;
                    that.$message({
                        type: "success",
                        message: "修改成功!",
                    });
                    // todo
                    that.form.iconUrl = "";
                    that.imgPath_ = "";
                    that.clearCondition();
                });
            } else {
                data.creatorId = window.localStorage.getItem("userName_");
                insertApplicationData(data, function (r) {
                    // that.$message.success('新增成功');
                    that.appFormVisible = false;
                    that.$message({
                        type: "success",
                        message: "新增成功!",
                    });
                    // todo
                    that.form.iconUrl = "";
                    that.imgPath_ = "";
                    that.clearCondition();
                });
            }
        },
        imgSuccess(res, file) {
            // 上传图片  file???
            // this.form.iconUrl = URL.createObjectURL(file.raw);
            debugger;
            // if (res.data == 1) {

            //     // 上传表单数据  todo  接口
            // }else{
            //     this.$message.error('上传图片失败！');
            // }
            this.up_loading = false;
            if (res.retCode === "1" || res.respCode === "") {
                this.form.iconUrl = res.value.url;
                this.submitForm();
            } else {
                debugger;

                this.$refs.upload.uploadFiles[0].status = "ready";
                this.$message.error("上传图片失败！");
                if (res.respCode == "2003" || res.respCode == "2004") {
                    this.$message.error(
                        res.respDesc || "登录超时，即将跳转至登录界面"
                    );
                    window.location.href = "#/";
                }
            }

            // todo test
            // this.form.iconUrl = "http://183.136.187.224:8090/appBacImg/test.png";
        },
        imgError(res, file) {
            this.up_loading = false;
            this.$refs.upload.uploadFiles[0].status = "ready";
            this.$message.error("上传图片失败！");
        },
        changeImg(file_) {
            this.$refs.upload.uploadFiles = [];
            debugger;
            if (file_.status == "success") {
                return;
            }
            let file = file_.raw;
            // 上传前的校验
            const isRightType =
                file.type === "image/jpeg" || file.type === "image/png";
            const isLt2M = file.size / 1024 / 1024 < 2;

            if (!isRightType) {
                this.$message.error("上传图片为jpg/png格式!");
                return;
            }
            if (!isLt2M) {
                this.$message.error("上传图片大小不能超过2MB!");
                return;
            }
            console.log("handlePreview");

            this.form.iconUrl = "true";
            this.imgPath_ = window.URL.createObjectURL(file);
            // this.imgPath_ = "http://183.136.187.224:8090/appBacImg/test.png";
            // document.querySelector("#img_apps").src=window.URL.createObjectURL(file);
            this.$refs.upload.uploadFiles.push(file_);
        },
        /* beforeAvatarUpload(file) {
                // 上传前的校验
                const isRightType = (file.type === 'image/jpeg') || (file.type === 'image/png') || (file.type === (file.type === 'image/jpg'));
                const isLt2M = file.size / 1024 / 1024 < 2;

                if (!isRightType) {
                    this.$message.error('上传图片为jpg/png格式!');
                }
                if (!isLt2M) {
                    this.$message.error('上传图片大小不能超过2MB!');
                }
                return isRightType && isLt2M;
            }, */

        cancleSubKm() {
            debugger;
            this.$refs.kmform.resetFields();
            this.appDialogVisible = false;
        },
        checkSecond() {
            let r = true;
            let that = this;
            this.secondList.forEach((it, index) => {
                debugger;
                if (!it.name.trim()) {
                    r = false;
                    that.secondList[index].isE = true;
                }
            });
            if (!r) {
                this.$message({
                    type: "error",
                    message: "二级科目不能为空!",
                });
            }
            return r;
        },

        onSubmitKm() {
            debugger;
            let result = true;
            this.$refs.kmform.validate((valid) => {
                debugger;
                if (!valid) {
                    result = false;
                }
            });
            if (result && this.checkSecond()) {
                // 检查二级科目
                if (this.isEditKm) {
                    this.updateKm();
                } else {
                    this.addKmL();
                }

                this.appDialogVisible = false;
                // todo
                // this.submitForm();
            } else {
                /* debugger;
                    this.$nextTick(()=>{
                        document.querySelector(".el-dialog").scrollTop =  document.querySelector(".el-form-item__error").parentNode.offsetTop - 40;
                    }); */

                return false;
            }
        },
        // 表单提交
        onSubmit() {
            let result = true;

            this.$refs.form.validate((valid) => {
                debugger;
                if (!valid) {
                    result = false;
                }
            });
            if (result) {
                this.submitForm();

                // todo
                // this.submitForm();
            } else {
                // 没有上传图片
                debugger;
                this.$nextTick(() => {
                    document.querySelector(".el-dialog__body").scrollTop =
                        document.querySelector(".el-form-item__error")
                            .parentNode.offsetTop - 40;
                });

                return false;
            }
        },

        async initData() {
            // 查询应用列表，获去tableData 初始化分页 数据字典
            try {
                this.getApps();
            } catch (err) {
                console.log("获取数据失败", err);
            }
        },
        handleSizeChange(val) {
            // console.log(`每页 ${val} 条`);
            this.limit = val;
            this.getApps();
        },
        handleCurrentChange(val) {
            this.currentPage = val;
            // this.offset = (val - 1)*this.limit;
            this.getApps();
        },
        getApps() {
            let that = this;
            that.loading = true;

            // todo test
            // that.loading = false;
            // return;

            getAppList(
                {
                    hospName: this.hospName,
                    levelCode: this.levelCode,
                    districtCode: this.districtCode,
                    pageNum: this.currentPage,
                    pageSize: this.limit,
                },
                function (res) {
                    // let data = res.data;
                    let data = res.value;
                    debugger;
                    that.loading = false;
                    that.tableData = [];
                    // 分页
                    that.count = data.total;
                    if (data.list && data.list.length > 0) {
                        that.tableData = data.list;
                    }

                    // 设置tableData todo
                    // data.list.forEach(item => {
                    //     const tableData = {};
                    //     tableData.username = item.username;
                    //     tableData.registe_time = item.registe_time;
                    //     tableData.city = item.city;
                    //     that.tableData.push(tableData);
                    // })
                }
            );
        },
    },
};
</script>

<style lang="less">
</style>
