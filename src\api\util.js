/*
 * @Descripttion: 
 * @version: 
 * @Author: changqing
 * @Date: 2020-05-19 11:11:56
 * @LastEditors: changqing
 * @LastEditTime: 2020-11-09 10:47:06
 */ 
export const common = {
    // hospurl:"http://*************:8080/medicalCheck/#/hospitalDetails/",
    // hospurl:"https://www.hfi-health.com:28181/medicalCheck/#/hospitalDetails/", //生成环境
    // hospurl:"http://***************:8011/medicalCheck/#/hospitalDetails/",
    hospurl:"https://www.hfi-health.com:28181/medicalCheck/#/hospitalDetails/",

    aesKey:"5E6711E155375218",
    aesUnCode(encryptedData){
        debugger;
        if(!encryptedData){
            return "";
        }
        let key = CryptoJS.enc.Utf8.parse(common.aesKey); 
        let decrypt = CryptoJS.AES.decrypt(encryptedData, key, {mode:CryptoJS.mode.ECB,padding: CryptoJS.pad.Pkcs7});
        return CryptoJS.enc.Utf8.stringify(decrypt).toString();
    },
    aesCode(str){
        debugger;
        if(str){
            if(typeof(str) == 'object'){
                str = JSON.stringify(str);
            }

            let key = CryptoJS.enc.Utf8.parse(common.aesKey); 

            let srcs = CryptoJS.enc.Utf8.parse(str);
            let encrypted = CryptoJS.AES.encrypt(srcs, key, {mode:CryptoJS.mode.ECB,padding: CryptoJS.pad.Pkcs7});
            return encrypted.toString();

        }else{
            return "";
        }
        
    },
     // 手机校验
     isPoneAvailable(num) {  
        var myreg=/^[1][0-9]{10}$/; 
        return  myreg.test(num);
    },
    // 身份证校验
    isCardNo(code){
        // function IdentityCodeValid(code) { 
        var city={11:"北京",12:"天津",13:"河北",14:"山西",15:"内蒙古",21:"辽宁",22:"吉林",23:"黑龙江 ",31:"上海",32:"江苏",33:"浙江",34:"安徽",35:"福建",36:"江西",37:"山东",41:"河南",42:"湖北 ",43:"湖南",44:"广东",45:"广西",46:"海南",50:"重庆",51:"四川",52:"贵州",53:"云南",54:"西藏 ",61:"陕西",62:"甘肃",63:"青海",64:"宁夏",65:"新疆",71:"台湾",81:"香港",82:"澳门",91:"国外 "};
        var tip = "";
        var pass= true;

        if(!code || !/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i.test(code)){
            tip = "身份证号格式错误";
            pass = false;
        }

       else if(!city[code.substr(0,2)]){
            tip = "地址编码错误";
            pass = false;
        }
        else{
            //18位身份证需要验证最后一位校验位
            if(code.length == 18){
                code = code.split('');
                //∑(ai×Wi)(mod 11)
                //加权因子
                var factor = [ 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2 ];
                //校验位
                var parity = [ 1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2 ];
                var sum = 0;
                var ai = 0;
                var wi = 0;
                for (var i = 0; i < 17; i++)
                {
                    ai = code[i];
                    wi = factor[i];
                    sum += ai * wi;
                }
                var last = parity[sum % 11];
                console.log(last);
                if(parity[sum % 11] != code[17]){
                    tip = "校验位错误";
                    pass =false;
                }
            }
        }
        // if(!pass) alert(tip);
        return pass;
        // }
       //  var c = '130981199312253466';
       // var res= IdentityCodeValid(c);



    },
    isName(name){
        var reg = /[\u4e00-\u9fa5]{2,10}/; 
        if (!reg.test(name)){
            return false;
        }
        return true;
    },

}