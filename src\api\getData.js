/*
 * @Descripttion: 
 * @version: 
 * @Author: changqing
 * @Date: 2019-07-04 10:55:48
 * @LastEditors: changqing
 * @LastEditTime: 2020-10-30 16:54:46
 */
import fetch from '@/config/fetch'

/**
 * 登陆
 */

export const login = (data,callback) => fetch('/yimei/user/login', data, 'POST',undefined,callback);
export const editPwd = (data,callback) => fetch('/yimei/shiro/manager/changePassword', data, 'POST',undefined,callback);

/**
 * 退出
 */

export const logout = (data,callback) => fetch('/yimei/user/logout', data, 'POST',undefined,callback);

// 获取数据字典
export const getDataDic = (data,callback,fail) => fetch('/yimei/front/dictionary/getAll',data,"post",undefined,callback,fail);

// 获取医院列表
export const getAppList = (data,callback) => fetch('/yimei/back/hospital/getList',data,"post",undefined,callback);

// insertApplicationData 新增医院
export const insertApplicationData = (data,callback) => fetch('/yimei/back/hospital/add',data,"post",undefined,callback);
// updateApplicationData 更新医院
export const updateApplicationData = (data,callback) => fetch('/yimei/back/hospital/update',data,"post",undefined,callback);
// 获取科目列表
export const getKmList = (data,callback) => fetch('/yimei/back/depart/getList',data,"get",undefined,callback);

// 更新科目
export const updateKmList = (data,callback) => fetch('/yimei/back/depart/update',data,"post",undefined,callback);
// 添加科目
export const addKmList = (data,callback) => fetch('/yimei/back/depart/add',data,"post",undefined,callback);
// 删除科目
export const deleteKmList = (data,callback) => fetch('/yimei/back/depart/deleteById',data,"get",undefined,callback);

// 查询医生列表
export const getDocList = (data,callback) => fetch('/yimei/back/doctor/getList',data,"get",undefined,callback);
export const addDoc = (data,callback) => fetch('/yimei/back/doctor/add',data,"post",undefined,callback);
export const updateDoc = (data,callback) => fetch('/yimei/back/doctor/update',data,"post",undefined,callback);
export const deleDoc = (data,callback) => fetch('/yimei/back/doctor/deleteById',data,"get",undefined,callback);

export const importMinkeDataAll = (data,callback) => fetch('/yimei/back/data/minke/importMinkeDataAll',data,"post",undefined,callback);

export const queryInformation = (data,callback) => fetch('/yimei/back/information/query',data,"post",undefined,callback);
export const addInformation = (data,callback) => fetch('/yimei/back/information/add',data,"post",undefined,callback);
export const deleteInformation = (data,callback) => fetch('/yimei/back/information/delete',data,"post",undefined,callback);
export const editInformation = (data,callback) => fetch('/yimei/back/information/update',data,"post",undefined,callback);

export const exportHospital = (data,callback) => fetch('/yimei/back/hospital/exportHospital',data,"post",undefined,callback);
export const exportDoctor = (data,callback) => fetch('/yimei/back/hospital/exportDoctor',data,"post",undefined,callback);

/**
 * 获取用户信息
 */

export const getAdminInfo = () => fetch('/admin/info');


    /**
     * [getAnotherDay 获取距离某个日期的指定类型的某一天]
     * @param  {[type]} type     [类型]
     * @param  {[type]} num      [差值]
     * @param  {[type]} isFormat [是否需要格式化]
     * @param  {[Date]} startDay [指定日期]
     * @return {[type]}          [description]
     * getAnotherDay('date', -1, true) 获取昨天+格式化
     */
export const  getAnotherDay = function(type, num, isFormat, startDay){
        var _day = startDay || new Date();
        switch(type){
            case 'year':
                _day = _day.setFullYear(_day.getFullYear() + num);
                break;
            case 'month':
                _day = _day.setMonth(_day.getMonth() + num);
                break;
            case 'date':
                _day = _day.setDate(_day.getDate() + num);
                break;
        }
        _day = new Date(_day);
        return !isFormat ? _day : _day.format('yyyy-MM-dd');
    }






