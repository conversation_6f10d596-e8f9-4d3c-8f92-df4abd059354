/*
 * @Descripttion: 
 * @version: 
 * @Author: changqing
 * @Date: 2019-07-04 10:55:48
 * @LastEditors: changqing
 * @LastEditTime: 2020-10-21 16:02:54
 */
import Vue from 'vue'
import App from './App'
import router from './router'
// import store from './store/'
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import {getDataDic} from '@/api/getData'
import "babel-polyfill"

    /*使用方法
     var now = new Date();
     var nowStr = now.format("yyyy-MM-dd hh:mm:ss");
     //使用方法2:
     var testDate = new Date();
     var testStr = testDate.format("YYYY年MM月dd日hh小时mm分ss秒");
     alert(testStr);
     //示例：
     alert(new Date().Format("yyyy年MM月dd日"));
     alert(new Date().Format("MM/dd/yyyy"));
     alert(new Date().Format("yyyyMMdd"));
     alert(new Date().Format("yyyy-MM-dd hh:mm:ss"));
     */
    Date.prototype.format = function(format) {
        var that = this;
        var o = {
            "M+": that.getMonth() + 1,
            //month
            "d+": that.getDate(),
            //day
            "h+": that.getHours(),
            //hour
            "m+": that.getMinutes(),
            //minute
            "s+": that.getSeconds(),
            //second
            "q+": Math.floor((that.getMonth() + 3) / 3),
            //quarter
            "S": that.getMilliseconds() //millisecond
        };

        if (/(y+)/.test(format)) {
            format = format.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        }

        for (var k in o) {
            if (new RegExp("(" + k + ")").test(format)) {
                format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
            }
        }
        return format;
    };

Vue.config.productionTip = false;

Vue.use(ElementUI);
router.beforeEach((to, from, next) => {
	debugger;
	// 缓存数据字典
	if((!window.localStorage.getItem("dataDic") || window.localStorage.getItem("dataDic") == "undefined") && to.path != "/" && to.path.indexOf("ymSys") > -1){
        debugger;
        getDataDic({},function(data){
            if(data.value){
                window.localStorage.setItem("dataDic", JSON.stringify(data.value));
            }
            next();
        },function(r){
            next();
        })
            
        
		
		// }).then(result => {
        //     console.log("next");
		// 	next();
		// });
	}else{
		next();
	}
	


})
new Vue({
	el: '#app',
	router,
	// store,
	template: '<App/>',
	components: { App }
})
