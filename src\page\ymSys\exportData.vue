<template>
    <div class="fillcontain">
        <head-top></head-top>
        <div class="table_container">
            <div style="margin-bottom:20px;">
                <!-- <el-input v-model="hospName" placeholder="请输入医疗机构名称" style="width:200px;" size="small"></el-input>
                <el-select v-model="districtCode" placeholder="请选择所属城区" style="width:150px;" size="small">
                <el-option
                    v-for="item in options_districtList"
                    :key="item.optionCode"
                    :label="item.optionName"
                    :value="item.optionCode">
                </el-option>
                </el-select>
                <el-select v-model="levelCode" placeholder="请选择机构类别" style="width:150px;" size="small">
                <el-option
                    v-for="item in options_institutionList"
                    :key="item.optionCode"
                    :label="item.optionName"
                    :value="item.optionCode">
                </el-option>
                </el-select> -->
                
                
                <el-button type="success"  icon="el-icon-download" size="small"  @click="exportHosp()">导出医院数据</el-button>
                <el-button type="warning"  icon="el-icon-download" size="small"  @click="exportDoc()">导出医生数据</el-button>
            </div>
        </div>
    </div>
</template>
<style>
.warning-row > input {
    /* background: red; */
    border: 1px solid red !important;
}
</style>

<script>
import headTop from '../../components/headTop'
import { baseUrl, baseImgPath } from '@/config/env'
import { Loading } from 'element-ui';
import { exportHospital,exportDoctor} from '@/api/getData'
import { common } from '../../api/util'
// import {mapDic} from 'vuex'
export default {
    data() {

        return {
            // isIng:false,
            options_districtList:[],
            options_institutionList:[],

            // 头部搜索条件
            hospName:"",
            levelCode:"",
            districtCode:"",


        }
    },
    components: {
        headTop,
    },
    created() {
        let that = this;


    },
    mounted() {
        debugger;
        // 应用状态 statusList 应用类别appTypeList 应用权限 authStatusList

        let dic = JSON.parse(window.localStorage.getItem("dataDic"));
        debugger;
        if(dic){
            this.options_districtList = dic.district;
            this.options_gradeList =  dic.grade;
            this.options_subjectsList =  dic.subjects;
            this.options_scopeList = dic.scope;
            this.options_institutionList =  dic.institution;
            this.options_yesNoList =  dic.yesNo;
        }

    },
    methods: {
        exportHosp(){

            window.loading_excel = this.$loading({
                lock: true,
                text: '数据导出中，请稍候...',
                spinner: 'el-icon-loading',
                background: 'rgba(256, 256, 256, 0.5)'
            });
            let that = this;
            exportHospital({
                "bizContent":{
                    /* hospName:this.hospName,
                    levelCode:this.levelCode,
                    districtCode:this.districtCode, */
                }
            },
            function(blob){
                // debugger;
                // that.loading = false;
                var url = window.URL.createObjectURL(blob);
                var a = document.createElement('a');
                a.href = url;
                a.download = "医院数据"+new Date().getDate()+".csv";
                a.click(); 
                loading_excel.close();
            });
        },
        exportDoc(){
            // exportDoctor
            window.loading_excel = this.$loading({
                lock: true,
                text: '数据导出中，请稍候...',
                spinner: 'el-icon-loading',
                background: 'rgba(256, 256, 256, 0.5)'
            });
            exportDoctor({
                "bizContent":{
                    
                }
            },
            function(blob){
                // debugger;
                // that.loading = false;
                var url = window.URL.createObjectURL(blob);
                var a = document.createElement('a');
                a.href = url;
                a.download = "医生数据"+new Date().getDate()+".csv";
                a.click(); 
                loading_excel.close();
            });
            
        },
        


    },
}
</script>

<style lang="less">
</style>
