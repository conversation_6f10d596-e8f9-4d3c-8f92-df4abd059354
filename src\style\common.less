body, div, span, header, footer, nav, section, aside, article, ul, dl, dt, dd, li, a, p, h1, h2, h3, h4,h5, h6, i, b, textarea, button, input, select, figure, figcaption {
    padding: 0;
    margin: 0;
    list-style: none;
    font-style: normal;
    text-decoration: none;
    border: none;
    font-family: "Microsoft Yahei",sans-serif;
    -webkit-tap-highlight-color:transparent;
    -webkit-font-smoothing: antialiased;
    &:focus {
        outline: none;
    }
}

/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/  
// ::-webkit-scrollbar  
// {  
//     width: 0px;  
//     height: 10px;  
//     background-color: rgb(245, 245, 245,0.5);  
// }  
  
/*定义滚动条轨道 内阴影+圆角*/  
// ::-webkit-scrollbar-track  
// {  
//     -webkit-box-shadow: inset 0 0 1px rgba(0,0,0,0);  
//     border-radius: 10px;  
//     background-color: #F5F5F5;  
// }  
  
/*定义滑块 内阴影+圆角*/  
// ::-webkit-scrollbar-thumb  
// {  
//     border-radius: 10px;  
//     -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);  
//     background-color: rgba(0, 0, 0, 0.5); 
// }  

input[type="button"], input[type="submit"], input[type="search"], input[type="reset"] {
    -webkit-appearance: none;
}

textarea { -webkit-appearance: none;}   

html,body{
    height: 100%;
    width: 100%;
    // background-color: #F5F5F5;
}

.fillcontain{
    height: 100%;
    width: 100%;
}
.clear:after{
    content: '';
    display: block;
    clear: both;
}

.clear{
    zoom:1;
}

.back_img{
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.margin{
    margin: 0 auto;
}

.left{
    float: left;
}

.right{
    float: right;
}

.hide{
    display: none;
}

.show{
    display: block;
}

.ellipsis{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/** style (注意不要设为scoped) */
/** configurationTable和afterRenderClass都是为了标记仅这个组件内修改 */
.configurationTable .el-table__body-wrapper {
    overflow: hidden;
}
.afterRenderClass {
    .el-table__body-wrapper {
        overflow: auto;
    }
}


.abow_dialog {
    display: flex;
    justify-content: center;
    align-items: Center;
    overflow: hidden;
    .el-dialog {
        margin: 0 auto !important;
        height: 90%;
        overflow: hidden;
        .el-dialog__body {
            position: absolute;
            left: 0;
            top: 54px;
            bottom: 0;
            right: 0;
            // padding: 0;
            z-index: 1;
            overflow: hidden;
            overflow-y: auto;
        }
    }
}

.fullScreen_dialog{
    .el-dialog {
        height: 100%;
        width: 60%;
    }
    
}

.confirmBox {
    button{
        margin:5px 20px;
    }
    .el-message-box__message p{
        font-size: 16px;
    }
}

.ml10{
    margin-left: 10px;
} 
.mb5{
    margin-bottom: 5px;
} 
.itemDiv{
    
    padding:5px;
    width: 100%;
}
.itemDiv.jItem{
    background:rgb(252, 252, 252);
}
.itemDiv:hover{
    background:rgb(236, 245, 255,0.3);
}


// body .el-table th.gutter{
//     display: table-cell!important;
//     width: 17px !important;
// }
@media only screen and (min-width: 1430px)and (max-width: 1600px){
    .el-col-4.menu_left_ {
        width: 15%;
    }
    .el-col-20.table_right_ {
        width: 85%;
    }
}
@media only screen and (min-width: 1600px)and (max-width: 1700px){
    .el-col-4.menu_left_ {
        width: 14%;
    }
    .el-col-20.table_right_ {
        width: 86%;
    }
}
@media only screen and (min-width: 1700px){
    .el-col-4.menu_left_ {
        width: 12.5%;
    }
    .el-col-20.table_right_ {
        width: 87.5%;
    }
}


.secondUl{
    width: 100%;
    display: flex;
    align-items: center;
    height: 50px;
    line-height: 50px;
    align-content: center;
    justify-content: space-between;
}

