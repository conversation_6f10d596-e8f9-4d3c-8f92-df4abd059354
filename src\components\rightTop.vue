<!--
 * @Descripttion: 
 * @version: 
 * @Author: changqing
 * @Date: 2019-11-14 14:51:42
 * @LastEditors: changqing
 * @LastEditTime: 2019-11-14 16:58:35
 -->
<template>
    <div class="header_container">
			
			<el-dropdown @command="handleCommand" menu-align='start'>
				<!-- <img :src="url" class="avator"> -->
                <span class="avator">
                    <i class="el-icon-user-solid" style="font-size:28px;"></i>
                    
                <!-- <span class="avator">{{userName}}</span> -->
                </span>
                
				<el-dropdown-menu slot="dropdown">
					<el-dropdown-item >{{userName}}</el-dropdown-item>
					<el-dropdown-item command="signout">退出</el-dropdown-item>
				</el-dropdown-menu>
			</el-dropdown>
    </div>
</template>

<script>
	import {logout} from '@/api/getData'
	import {baseImgPath} from '@/config/env'
	// import {mapActions, mapState} from 'vuex'

    export default {
    	data(){
    		return {
                userName:""	
    		}
    	},
    	created(){
    		// if (!this.adminInfo.id) {
			// 	this.getAdminData();
			// 	this.url = require("../img/"+this.adminInfo.avatar);	
            // }
            this.userName = window.localStorage.getItem("userName_");
            
    	},
    	computed: {
    		// ...mapState(['adminInfo']),
    	},
		methods: {
			// ...mapActions(['getAdminData']),
			async handleCommand(command) {
				if (command == 'home') {
					this.$router.push('/manage');
				}else if(command == 'signout'){
                    let that = this;
					const res = await logout(
                            {
                                "appId": "a000002",
                                "logTraceID":"aaaaasssssdddddfffffggggghhhh103",
                                "method": "queryActive",
                                "sign": "",
                                "bizContent": {
                                },
                                "time": "123",
                                "reqSeqNo": "456"
                            }
                            /*{"bizContent":{username: this.loginForm.username, password: this.loginForm.password}}*/,
                            function(res){
                                that.$message({
                                    type: 'success',
                                    message: '退出成功'
                                });
                                that.$router.push('/')

                            }
                        )
				}
			},
		}
    }
</script>

<style lang="less" scoped>
	@import '../style/mixin';
	.header_container{
		// background-color: #EFF2F7;
		height: 60px;
		display: flex;
		justify-content: flex-end;
		align-items: center;
		padding-left: 20px;
	}
	.avator{
		.wh(36px, 36px);
		border-radius: 50%;
		margin-right: 37px;
	}
	.el-dropdown-menu__item{
        text-align: center;
    }
</style>
