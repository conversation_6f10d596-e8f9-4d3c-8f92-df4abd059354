<template>
  	<div class="login_page fillcontain">
	  	<transition name="form-fade" mode="in-out">
	  		<section class="form_contianer" v-show="showLogin">
		  		<div class="manage_tip">
		  			<p>修改密码</p>
		  		</div>
		    	<el-form :model="editForm" :rules="rules" 
                ref="editForm" @keyup.enter.native="submitForm('editForm')">
					<el-form-item prop="username">
                        <el-input v-model="editForm.username" placeholder="用户名"></el-input>
                    </el-form-item>
					<el-form-item prop="oldPwd" >
						<el-input type="password" placeholder="请输入原始密码" v-model="editForm.oldPwd"></el-input>
					</el-form-item>
                    <el-form-item prop="newPwd">
						<el-input type="password" placeholder="请输入新密码" v-model="editForm.newPwd"></el-input>
					</el-form-item>
                    <el-form-item prop="sedPwd" >
						<el-input type="password" placeholder="请再次输入新密码" v-model="editForm.sedPwd"></el-input>
					</el-form-item>
					<el-form-item>
				    	<el-button type="primary" @click="submitForm('editForm')"   class="submit_btn">确定</el-button>
				  	</el-form-item>
				</el-form>
                <span class="linkP" @click="gotoLogin">登录</span>
				
	  		</section>
	  	</transition>
  	</div>
</template>

<script>
    import {editPwd, getAdminInfo} from '@/api/getData'
    import { common } from '../api/util';
	// import {mapActions, mapState} from 'vuex'

	export default {
	    data(){
            let that = this;
            let checksedPwd = (rule, value, callback) => {
                debugger;
                if(that.editForm.newPwd !== that.editForm.sedPwd){
                    callback(new Error('两次密码输入不一致'));
                }else{
                    callback();
                }
            };
            
            let checkPwd = (rule, value, callback) => {
                debugger;
                if(that.editForm.newPwd === that.editForm.oldPwd ){
                    callback(new Error('新密码与旧密码一致，请重新输入'));
                }
                if(that.editForm.newPwd){
                    let reg = /(?=.*([a-zA-Z].*))(?=.*[0-9].*)[a-zA-Z0-9-*/+.~!@#$%^&*()]{6,16}$/;
                    if(reg.test(that.editForm.newPwd)){
                        callback();
                    }else{
                        callback(new Error('密码长度为6-16位，至少包含数字跟字母，可以有字符:-*/+.~!@#$%^&*()'));
                    }
                    
                }else{
                    callback(new Error('请输入新的密码'));
                }
            };
			return {
				editForm: {
					username: '',
                    oldPwd: '',
                    newPwd:'',
                    sedPwd:"",
				},
				rules: {
					username: [
			            { required: true, message: '请输入用户名', trigger: 'blur' },
			        ],
					oldPwd: [
						{ required: true, message: '请输入原始密码', trigger: 'blur' }
                    ],
                    newPwd: [
                        { required: true, message: '请输入新密码', trigger: 'blur'},
                        { validator: checkPwd, trigger: 'blur' }
                    ],
                    sedPwd: [
                        { validator: checksedPwd, trigger: 'blur' }
                    ],

				},
				showLogin: false,
			}
		},
		mounted(){
            this.showLogin = true;
            this.editForm.username = window.localStorage.getItem("userName_");
            this.editForm.oldPwd = "";
			// if (!this.adminInfo.id) {
    		// 	this.getAdminData()
    		// }
		},
		computed: {
			// ...mapState(['adminInfo']),
		},
		methods: {
			// ...mapActions(['getAdminData']),
			async submitForm(formName) {
                let that = this;
                // window.localStorage.setItem("token","-T9-@hJ@ahlhh5eh5TT5e25--2-@T*Ja");
				this.$refs[formName].validate(async (valid) => {
					if (valid) {
						const res = await editPwd(
                            {
                                "appId": "a000002",
                                "logTraceID":"aaaaasssssdddddfffffggggghhhh103",
                                "method": "queryActive",
                                "sign": "",
                                "bizContent": {
                                    username: this.editForm.username, 
                                    password: common.aesCode(this.editForm.oldPwd),
                                    newUserPwd:common.aesCode(this.editForm.newPwd),
                                },
                                "time": "123",
                                "reqSeqNo": "456"
                            }
                            /*{"bizContent":{username: this.editForm.username, password: this.editForm.password}}*/,
                            function(res){
                                debugger;

                                that.$alert('修改密码成功，请重新登录吧', '提示', {
                                    confirmButtonText: '确定',
                                    callback: action => {
                                        that.$router.push('/');
                                    }
                                });
                                
                                
                                
                                

                            }
                        )
                        
                        
					} else {
						this.$notify.error({
							title: '错误',
							message: '请输入正确的用户名密码',
							offset: 100
						});
						return false;
					}
				});
            },
            gotoLogin(){
                this.$router.push('/');
            }
		},
		watch: {
		// 	adminInfo: function (newValue){
		// 		debugger;
		// 		if (newValue.id) {
		// 			this.$message({
        //                 type: 'success',
        //                 message: '检测到您之前登录过，将自动登录'
        //             });
		// 			this.$router.push('appList')
		// 		}
		// 	}
		}
	}
</script>

<style lang="less" scoped>
	@import '../style/mixin';
	.login_page{
		background-color: #324057;
	}
	.manage_tip{
		position: absolute;
		width: 100%;
		top: -80px;
		left: 0;
		p{
			font-size: 34px;
			color: #fff;
		}
	}
	.form_contianer{
		.wh(500px, 320px);
		.ctp(550px, 370px);
		padding: 25px;
		border-radius: 5px;
		text-align: center;
		background-color: #fff;
		.submit_btn{
			width: 100%;
			font-size: 16px;
		}
	}
	.tip{
		font-size: 12px;
		color: red;
	}
	.form-fade-enter-active, .form-fade-leave-active {
	  	transition: all 1s;
	}
	.form-fade-enter, .form-fade-leave-active {
	  	transform: translate3d(0, -50px, 0);
	  	opacity: 0;
    }
    .linkP{
        float: right;
        font-size: 14px;
        color: #2e82ff;
        cursor: pointer;
    }
    .linkP:hover{
        opacity: .6;
    }
</style>
