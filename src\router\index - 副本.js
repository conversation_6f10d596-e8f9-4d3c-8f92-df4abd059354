/*
 * @Descripttion: 
 * @version: 
 * @Author: chang<PERSON>
 * @Date: 2019-07-04 10:55:48
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2021-10-25 10:49:12
 */
import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

const login = r => require.ensure([], () => r(require('@/page/login')), 'login');
const editPwd = r => require.ensure([], () => r(require('@/page/editPwd')), 'editPwd');
const manage = r => require.ensure([], () => r(require('@/page/ymSys/manage')), 'manage');

// 应用
const infoList = r => require.ensure([], () => r(require('@/page/ymSys/infoList')), 'infoList');

const caseList = r => require.ensure([], () => r(require('@/page/ymSys/caseList')), 'caseList');



const routes = [
	{
		path: '/',
		component: login
    },
    {
		path: '/editPwd',
		component: editPwd
    },
	{
		path: '/ymSys/manage',
		component: manage,
		name: '',
		children: [{
			path: '/ymSys/infoList',
			component: infoList,
			meta: ['基本信息管理'],
		},
		
		{
			path: '/ymSys/qxList',
			component: caseList,
			meta: ['医疗美容器械咨讯'],
		},
		{
			path: '/ymSys/caseList',
			component: caseList,
			meta: ['非法医疗美容案例'],
		}]
	}
]

export default new Router({
	routes,
	strict: process.env.NODE_ENV !== 'production',
})
