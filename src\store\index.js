import Vue from 'vue'
import Vuex from 'vuex'
import {getAdminInfo} from '@/api/getData'

Vue.use(Vuex)

const state = {
	adminInfo: {
		avatar: 'default.png'
    },
    dic:{
        list:JSON.parse(window.sessionStorage.getItem("dataDic"))
    }
}

const mutations = {
	saveAdminInfo(state, adminInfo){
		state.adminInfo = adminInfo;
	}
}

// const dic = JSON.parse(window.sessionStorage.getItem("dataDic"));

const actions = {
	async getAdminData({commit}){
		try{
			// const res = await getAdminInfo()
			const res = {avatar: 'default.jpg'}
			if (res.status == 1) {
				commit('saveAdminInfo', res.data);
			}else{
				throw new Error(res.type)
			}
		}catch(err){
			// console.log(err.message)
		}
	}
}

export default new Vuex.Store({
	state,
	actions,
    mutations
})
