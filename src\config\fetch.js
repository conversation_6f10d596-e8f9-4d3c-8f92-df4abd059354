import { baseUrl } from './env'
import { Loading,Message } from 'element-ui';

export default async(url = '', data = {}, type = 'GET', method = 'fetch',callBack,failure) => {
	type = type.toUpperCase();
	url = url + "?random=" + Math.random();
	const loading = Loading.service({
        background: 'rgba(0, 0, 0, 0.3)'
	});
	debugger;

	if (type == 'GET' ) {
		let dataStr = ''; //数据拼接字符串
		Object.keys(data).forEach(key => {
			dataStr += key + '=' + data[key] + '&';
		})

		if (dataStr !== '') {
			dataStr = dataStr.substr(0, dataStr.lastIndexOf('&'));
			url = url + '&' + dataStr;
		}
	}

	if (window.fetch && method == 'fetch') {
		let requestConfig = {
			credentials: 'include',
			method: type,
			headers: {
                'token':window.localStorage.getItem("token"),
                // 'token':"a*26*ee-52l-a-l52-2hT*656t*9-@-9",
				'Accept': 'application/json',
				'Content-Type': 'application/json'
			},
			mode: "cors",
			cache: "force-cache"
        }
        

		if (type == 'POST'  || type == 'DELETE') {
			Object.defineProperty(requestConfig, 'body', {
				value: JSON.stringify(data)
			})
		}
		
		try {
			const response = await fetch(url, requestConfig);
			debugger;
            loading.close();
            
			if(response.status === 200){
                if(url.indexOf("exportActivityResult")>-1 || url.indexOf("exportHospital")>-1 || url.indexOf("exportDoctor")>-1){
                    const blob = await response.blob();
                    callBack(blob);
                }else{
                    const responseJson = await response.json();
				// 处理数据
				if(responseJson.retCode === "1" || (responseJson.respCode==="" && responseJson.success == 1)){
					callBack(responseJson);
					// return responseJson.data
				}else{
                    if(responseJson.respCode == "2004" || responseJson.respCode == "2003" || (responseJson.respCode == "2002" && window.location.hash != "#/")){
                        // todo  2002 有问题 等后台排查
                        
                        // 登录失效
                        Message.error(responseJson.respDesc || '登录超时，即将跳转至登录界面');
                        // setTimeout(function(){
                        
                        // },200);
                        if(failure){
                            failure(responseJson);
                        }
                        window.location.href = "#/";
                        
                    }else{
                        if(failure){
                            failure(responseJson);
                        }else{
                            Message.error(responseJson.respDesc || responseJson.retMsg || '数据加载失败，请稍后再试~');
                        }
                    }

                }
                


				
					
				}
			}else{
                // Message.error(response.statusText || '数据加载失败，请稍后再试~');
                Message.error('数据加载失败，请稍后再试~');
			}
			
		} catch (error) {
			loading.close();
			throw new Error(error)
		}
	} else {
		let requestObj;
		if (window.XMLHttpRequest) {
			requestObj = new XMLHttpRequest();
		} else {
			requestObj = new ActiveXObject;
		}

		let sendData = '';
		if (type == 'POST') {
			sendData = JSON.stringify(data);
		}

		requestObj.open(type, url, true);
        requestObj.setRequestHeader("Content-type", "application/json");
        requestObj.setRequestHeader("token", window.localStorage.getItem("token"));
		requestObj.send(sendData);

		requestObj.onreadystatechange = () => {
			if (requestObj.readyState == 4) {
				if (requestObj.status == 200) {
					let obj = requestObj.response
					if (typeof obj !== 'object') {
						obj = JSON.parse(obj);
					}
					loading.close();
					// 处理数据
					if(obj.retCode === "1" ||  obj.respCode===""){
						callBack(obj);
						// return responseJson.data
					}else{
						if(obj.respCode == "2004" || obj.respCode == "2003"){
                            // 登录失效
                            Message.error(obj.respDesc || '数据加载失败，请稍后再试~');
                            setTimeout(function(){
                                window.location.replace("#/login");
                            },500);
                            
                        }else{
                            if(failure){
                                failure(obj);
                            }else{
                                Message.error(obj.respDesc || obj.retMsg || '数据加载失败，请稍后再试~');
                            }
                        }
                    }
                    

                    
					
				} else {
					loading.close();
                    // Message.error(requestObj.statusText || '数据加载失败，请稍后再试~');
                    Message.error('数据加载失败，请稍后再试~');
				}
			}
		}
	}
}