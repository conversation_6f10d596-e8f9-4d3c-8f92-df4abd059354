/*******************************************************************************
* KindEditor - WYSIWYG HTML Editor for Internet
* Copyright (C) 2006-2011 kindsoft.net
*
* <AUTHOR> <<EMAIL>>
* @site http://www.kindsoft.net/
* @licence http://www.kindsoft.net/license.php
*******************************************************************************/

KindEditor.lang({
	source : '소스',
	preview : '미리보기',
	undo : '작업취소(Ctrl+Z)',
	redo : '작업재개(Ctrl+Y)',
	cut : '잘라내기(Ctrl+X)',
	copy : '복사(Ctrl+C)',
	paste : '붙여넣기(Ctrl+V)',
	plainpaste : '일반 텍스트로 붙여넣기',
	wordpaste : '워드 문서로 붙여넣기',
	selectall : '전체 선택',
	justifyleft : '왼쪽 정렬',
	justifycenter : '가운데 정렬',
	justifyright : '오른쪽 정렬',
	justifyfull : '양쪽 정렬',
	insertorderedlist : '순서 목록',
	insertunorderedlist : '비순서 목록',
	indent : '들여쓰기',
	outdent : '내어쓰기',
	subscript : '아랫첨자',
	superscript : '윗첨자',
	formatblock : '문단 형식',
	fontname : '글꼴',
	fontsize : '글자 크기',
	forecolor : '글자색',
	hilitecolor : '강조색',
	bold : '굵게(Ctrl+B)',
	italic : '이텔릭(Ctrl+I)',
	underline : '빝줄(Ctrl+U)',
	strikethrough : '취소선',
	removeformat : '형식 제거',
	image : '이미지 추가',
	multiimage : '여러 이미지 추가',
	flash : '플래시 추가',
	media : '미디어 추가',
	table : '표',
	tablecell : '열',
	hr : '구분선 추가',
	emoticons : '이모티콘 추가',
	link : '링크',
	unlink : '링크 제거',
	fullscreen : '전체 화면 모드',
	about : '이 에디터는...',
	print : '인쇄',
	filemanager : '파일 관리자',
	code : '코드 추가',
	map : '구글 맵 추가',
	baidumap : '바이두 맵 추가',
	lineheight : '행 간격',
	clearhtml : 'HTML 코드 정리',
	pagebreak : '페이지 구분 추가',
	quickformat : '빠른 형식',
	insertfile : '파일 추가',
	template : '템플릿 추가',
	anchor : '책갈피',
	yes : '확인',
	no : '취소',
	close : '닫기',
	editImage : '이미지 속성',
	deleteImage : '이미지 삭제',
	editFlash : '플래시 속성',
	deleteFlash : '플래시 삭제',
	editMedia : '미디어 속성',
	deleteMedia : '미디어 삭제',
	editLink : '링크 속성',
	deleteLink : '링크 삭제',
	editAnchor : 'Anchor properties',
	deleteAnchor : 'Delete Anchor',
	tableprop : '표 속성',
	tablecellprop : '열 속성',
	tableinsert : '표 추가',
	tabledelete : '표 삭제',
	tablecolinsertleft : '왼쪽으로 열 추가',
	tablecolinsertright : '오른쪽으로 열 추가',
	tablerowinsertabove : '위쪽으로 열 추가',
	tablerowinsertbelow : '아래쪽으로 열 추가',
	tablerowmerge : '아래로 병합',
	tablecolmerge : '오른쪽으로 병합',
	tablerowsplit : '행 나누기',
	tablecolsplit : '열 나누기',
	tablecoldelete : '열 삭제',
	tablerowdelete : '행 삭제',
	noColor : '기본색',
	pleaseSelectFile : '파일 선택',
	invalidImg : "올바른 주소를 입력하세요.\njpg,gif,bmp,png 형식이 가능합니다.",
	invalidMedia : "올바른 주소를 입력하세요.\nswf,flv,mp3,wav,wma,wmv,mid,avi,mpg,asf,rm,rmvb 형식이 가능합니다.",
	invalidWidth : "넓이 값은 숫자여야 합니다.",
	invalidHeight : "높이 값은 숫자여야 합니다.",
	invalidBorder : "굵기 값은 숫자여야 합니다.",
	invalidUrl : "올바른 주소를 입력하세요.",
	invalidRows : '올바른 행이 아닙니다.',
	invalidCols : '올바른 열이 아닙니다.',
	invalidPadding : '안쪽 여백 값은 숫자여야 합니다.',
	invalidSpacing : '간격 길이 값은 숫자여야 합니다.',
	invalidJson : '올바른 JSON 형식이 아닙니다.',
	uploadSuccess : '업로드가 완료되었습니다.',
	cutError : '브라우저가 잘라내기 기능을 지원하지 않습니다, 단축키로 대신 사용하세요. (Ctrl+X)',
	copyError : '브라우저가 복사 기능을 지원하지 않습니다, 단축키로 대신 사용하세요. (Ctrl+X)',
	pasteError : '브라우저가 붙여넣기 기능을 지원하지 않습니다, 단축키로 대신 사용하세요. (Ctrl+X)',
	ajaxLoading : '불러오는 중 ...',
	uploadLoading : '업로드 중 ...',
	uploadError : '업로드 오류',
	'plainpaste.comment' : '단축키(Ctrl+V)를 통하여 여기에 텍스트를 붙여넣으세요.',
	'wordpaste.comment' : '단축키(Ctrl+V)를 통하여 여기에 워드 텍스트를 붙여넣으세요.',
	'code.pleaseInput' : 'Please input code.',
	'link.url' : '주소',
	'link.linkType' : '창',
	'link.newWindow' : '새 창',
	'link.selfWindow' : '현재 창',
	'flash.url' : '주소',
	'flash.width' : '넓이',
	'flash.height' : '높이',
	'flash.upload' : '업로드',
	'flash.viewServer' : '찾아보기',
	'media.url' : '주소',
	'media.width' : '넓이',
	'media.height' : '높이',
	'media.autostart' : '자동 시작',
	'media.upload' : '업로드',
	'media.viewServer' : '찾아보기',
	'image.remoteImage' : '외부 이미지',
	'image.localImage' : '내부 이미지',
	'image.remoteUrl' : '주소',
	'image.localUrl' : '파일',
	'image.size' : '크기',
	'image.width' : '넓이',
	'image.height' : '높이',
	'image.resetSize' : '기본 크기로',
	'image.align' : '정렬',
	'image.defaultAlign' : '기본',
	'image.leftAlign' : '왼쪽',
	'image.rightAlign' : '오른쪽',
	'image.imgTitle' : '제목',
	'image.upload' : '찾아보기',
	'image.viewServer' : '찾아보기',
	'multiimage.uploadDesc' : '최대 이미지 개수: <%=uploadLimit%>개, 개당 이미지 크기: <%=sizeLimit%>',
	'multiimage.startUpload' : '업로드 시작',
	'multiimage.clearAll' : '모두 삭제',
	'multiimage.insertAll' : '모두 삽입',
	'multiimage.queueLimitExceeded' : '업로드 개수가 초과되었습니다.',
	'multiimage.fileExceedsSizeLimit' : '업로드 크기가 초과되었습니다.',
	'multiimage.zeroByteFile' : '파일 크기가 없습니다.',
	'multiimage.invalidFiletype' : '올바른 이미지가 아닙니다.',
	'multiimage.unknownError' : '알 수 없는 업로드 오류가 발생하였습니다.',
	'multiimage.pending' : '처리 중 ...',
	'multiimage.uploadError' : '업로드 오류',
	'filemanager.emptyFolder' : '빈 폴더',
	'filemanager.moveup' : '위로',
	'filemanager.viewType' : '보기 방식: ',
	'filemanager.viewImage' : '미리 보기',
	'filemanager.listImage' : '목록',
	'filemanager.orderType' : '정렬 방식: ',
	'filemanager.fileName' : '이름별',
	'filemanager.fileSize' : '크기별',
	'filemanager.fileType' : '종류별',
	'insertfile.url' : '주소',
	'insertfile.title' : '제목',
	'insertfile.upload' : '업로드',
	'insertfile.viewServer' : '찾아보기',
	'table.cells' : '열',
	'table.rows' : '행',
	'table.cols' : '열',
	'table.size' : '표 크기',
	'table.width' : '넓이',
	'table.height' : '높이',
	'table.percent' : '%',
	'table.px' : 'px',
	'table.space' : '간격',
	'table.padding' : '안쪽여백',
	'table.spacing' : '간격',
	'table.align' : '정렬',
	'table.textAlign' : '수직',
	'table.verticalAlign' : '수평',
	'table.alignDefault' : '기본',
	'table.alignLeft' : '왼쪽',
	'table.alignCenter' : '가운데',
	'table.alignRight' : '오른쪽',
	'table.alignTop' : '위쪽',
	'table.alignMiddle' : '중간',
	'table.alignBottom' : '아래쪽',
	'table.alignBaseline' : '글자기준',
	'table.border' : '테두리',
	'table.borderWidth' : '크기',
	'table.borderColor' : '색상',
	'table.backgroundColor' : '배경',
	'map.address' : '주소: ',
	'map.search' : '검색',
	'baidumap.address' : '주소: ',
	'baidumap.search' : '검색',
	'baidumap.insertDynamicMap' : '동적 지도',
	'anchor.name' : '책갈피명',
	'formatblock.formatBlock' : {
		h1 : '제목 1',
		h2 : '제목 2',
		h3 : '제목 3',
		h4 : '제목 4',
		p : '본문'
	},
	'fontname.fontName' : {
		'Gulim' : '굴림',
		'Dotum' : '돋움',
		'Batang' : '바탕',
		'Gungsuh' : '궁서',
		'Malgun Gothic' : '맑은 고딕',
		'Arial' : 'Arial',
		'Arial Black' : 'Arial Black',
		'Comic Sans MS' : 'Comic Sans MS',
		'Courier New' : 'Courier New',
		'Garamond' : 'Garamond',
		'Georgia' : 'Georgia',
		'Tahoma' : 'Tahoma',
		'Times New Roman' : 'Times New Roman',
		'Trebuchet MS' : 'Trebuchet MS',
		'Verdana' : 'Verdana'
	},
	'lineheight.lineHeight' : [
		{'1' : '행간 1'},
		{'1.5' : '행간 1.5'},
		{'2' : '행간 2'},
		{'2.5' : '행간 2.5'},
		{'3' : '행간 3'}
	],
	'template.selectTemplate' : '템플릿',
	'template.replaceContent' : '내용 바꾸기',
	'template.fileList' : {
		'1.html' : '이미지와 텍스트',
		'2.html' : '표',
		'3.html' : '목록'
	}
}, 'ko');

KindEditor.each(KindEditor.options.items, function(i, name) {
	if (name == 'baidumap') {
		KindEditor.options.items[i] = 'map';
	}
});
KindEditor.options.langType = 'ko';
