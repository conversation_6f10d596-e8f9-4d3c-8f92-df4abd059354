<template>
    <div class="fillcontain">
        <head-top></head-top>
        <div class="table_container">
            <p style="color:#E6A23C;">
                注：由于接口同步数据需要时间，请耐心等待，请勿短时间内多次执行~
                <br>
            </p>
            <div style="margin-top:20px;">
                <el-button type="danger" @click="startImp()" :disabled="isIng">一键执行</el-button>
            </div>
        </div>
    </div>
</template>
<style>
.warning-row > input {
    /* background: red; */
    border: 1px solid red !important;
}
</style>

<script>
import headTop from '../../components/headTop'
import { baseUrl, baseImgPath } from '@/config/env'
import { Loading } from 'element-ui';
import { importMinkeDataAll} from '@/api/getData'
import { common } from '../../api/util'
// import {mapDic} from 'vuex'
export default {
    data() {

        return {
            isIng:false,


        }
    },
    components: {
        headTop,
    },
    created() {
        let that = this;


    },
    mounted() {
        debugger;
        // 应用状态 statusList 应用类别appTypeList 应用权限 authStatusList

    },
    methods: {
        setIn(){
            // 60*60*1000
            let that = this;
            setTimeout(function(){
                that.isIng = false;
            },60*60*1000)
        },
        startImp(){
            let that = this;
            if(that.isIng){
                that.$message({
                    type: 'warning',
                    message: '数据同步中，请稍候再试~'
                });
                return
            }
            importMinkeDataAll({},function(r){
                that.isIng = true;
                that.setIn();
                that.$message({
                    type: 'success',
                    message: '执行请求已发送~'
                });
            });
        }


    },
}
</script>

<style lang="less">
</style>
