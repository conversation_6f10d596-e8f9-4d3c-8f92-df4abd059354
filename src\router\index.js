/*
 * @Descripttion: 
 * @version: 
 * @Author: changqing
 * @Date: 2019-07-04 10:55:48
 * @LastEditors: changqing
 * @LastEditTime: 2020-10-21 15:55:24
 */
import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

const login = r => require.ensure([], () => r(require('@/page/login')), 'login');
const editPwd = r => require.ensure([], () => r(require('@/page/editPwd')), 'editPwd');
const manage = r => require.ensure([], () => r(require('@/page/ymSys/manage')), 'manage');

// 应用
const infoList = r => require.ensure([], () => r(require('@/page/ymSys/infoList')), 'infoList');
const impData = r => require.ensure([], () => r(require('@/page/ymSys/impData')), 'impData');
const zxList = r => require.ensure([], () => r(require('@/page/ymSys/zxList')), 'zxList');
const exportData = r => require.ensure([], () => r(require('@/page/ymSys/exportData')), 'exportData');

const routes = [
	{
		path: '/',
		component: login
    },
    {
		path: '/editPwd',
		component: editPwd
    },
	{
		path: '/ymSys/manage',
		component: manage,
		name: '',
		children: [{
			path: '/ymSys/infoList',
			component: infoList,
			meta: ['基本信息管理'],
		},{
			path: '/ymSys/exportData',
			component: exportData,
			meta: ['数据导出'],
		},,{
			path: '/ymSys/impData',
			component: impData,
			meta: ['民科数据接口导入'],
		},{
			path: '/ymSys/qxList',
			component: zxList,
			meta: ['医疗美容器械咨讯'],
		},{
			path: '/ymSys/caseList',
			component: zxList,
			meta: ['非法医疗美容案例'],
		},
		
	]
	}
]

export default new Router({
	routes,
	strict: process.env.NODE_ENV !== 'production',
})
