@blue: #3190e8;  
@bc: #e4e4e4;
@fc:#fff;

// 背景图片地址和大小
.bis(@url) { 
	background-image: url(@url);
	background-repeat: no-repeat;
	background-size: 100% 100%;
}

//定位全屏
.allcover{
	position:absolute;
	top:0;
	right:0;
}

//transform上下左右居中
.ctt {  
	position: absolute;
	top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
//定位上下左右居中
.ctp(@width, @height) {  
	position: absolute;
	top: 50%;
    left: 50%;
    margin-top: -@height/2;
    margin-left: -@width/2;
}

//定位上下居中
.tb {  
	position: absolute;
	top: 50%;
    transform: translateY(-50%);
}

//定位左右居中
.lr {  
	position: absolute;
	left: 50%;
    transform: translateX(-50%);
}

//宽高
.wh(@width, @height){
	width: @width;
	height: @height;
}

//字体大小、行高、字体
.ft(@size, @line-height) {  
	font-size: @size;
	line-height:@line-height;
}

//字体大小，颜色
.sc(@size, @color){
	font-size: @size;
	color: @color;
}

//flex 布局和 子元素 对其方式
.fj(@type: space-between){
	display: flex;
	justify-content: @type;

}


@import '../style/mixin';
    .demo-table-expand {
        font-size: 0;
    }
    .demo-table-expand label {
        width: 90px;
        color: #99a9bf;
    }
    .demo-table-expand .el-form-item {
        margin-right: 0;
        margin-bottom: 0;
        width: 50%;
    }
    .table_container{
        padding: 20px;
    }
    .Pagination{
        display: flex;
        justify-content: flex-start;
        margin-top: 8px;
    }
    .avatar-uploader .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
    .avatar-uploader .el-upload:hover {
        border-color: #20a0ff;
    }
    .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 120px;
        height: 120px;
        line-height: 120px;
        text-align: center;
    }
    .avatar {
        width: 120px;
        height: 120px;
        display: block;
    }
