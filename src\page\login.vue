<template>
  	<div class="login_page fillcontain">
	  	<transition name="form-fade" mode="in-out">
	  		<section class="form_contianer" v-show="showLogin">
		  		<div class="manage_tip">
		  			<p>后台管理平台</p>
		  		</div>
		    	<el-form :model="loginForm" :rules="rules" ref="loginForm" @keyup.enter.native="submitForm('loginForm')">
					<el-form-item prop="username" >
						<el-input v-model="loginForm.username" placeholder="用户名"></el-input>
					</el-form-item>
					<el-form-item prop="password">
						<el-input type="password" placeholder="密码" v-model="loginForm.password"></el-input>
					</el-form-item>
					<el-form-item>
				    	<el-button type="primary" @click="submitForm('loginForm')"   class="submit_btn">登录</el-button>
				  	</el-form-item>
				</el-form>
                <span class="linkP" @click="gotoEditPwd">修改密码</span>
				<!-- <p class="tip">温馨提示：</p>
				<p class="tip">未登录过的新用户，自动注册</p>
				<p class="tip">注册过的用户可凭账号密码登录</p> -->
	  		</section>
	  	</transition>
  	</div>
</template>

<script>
	import {login, getAdminInfo} from '@/api/getData'
    import { common } from '../api/util';
	// import {mapActions, mapState} from 'vuex'

	export default {
	    data(){
			return {
				loginForm: {
					username: '',
					password: '',
				},
				rules: {
					username: [
			            { required: true, message: '请输入用户名', trigger: 'blur' },
			        ],
					password: [
						{ required: true, message: '请输入密码', trigger: 'blur' }
					],
				},
				showLogin: false,
			}
		},
		mounted(){
			this.showLogin = true;
			// if (!this.adminInfo.id) {
    		// 	this.getAdminData()
    		// }
		},
		computed: {
			// ...mapState(['adminInfo']),
		},
		methods: {
			// ...mapActions(['getAdminData']),
			async submitForm(formName) {
                let that = this;
                // window.localStorage.setItem("token","-T9-@hJ@ahlhh5eh5TT5e25--2-@T*Ja");
				this.$refs[formName].validate(async (valid) => {
					if (valid) {
						const res = await login(
                            {
                                "appId": "a000002",
                                "logTraceID":"aaaaasssssdddddfffffggggghhhh103",
                                "method": "queryActive",
                                "sign": "",
                                "bizContent": {
                                    username: this.loginForm.username, password: common.aesCode(this.loginForm.password) 
                                },
                                "time": "123",
                                "reqSeqNo": "456"
                            }
                            /*{"bizContent":{username: this.loginForm.username, password: this.loginForm.password}}*/,
                            function(res){
                                window.localStorage.setItem("dataDic", "");
                                debugger;
                                if(res.value.isInitPwd){
                                    // todo 跳转到修改密码页面
                                    that.$alert('检测到当前用户的为初始密码，为了安全，请修改密码', '提示', {
                                        confirmButtonText: '确定',
                                        callback: action => {
                                            window.localStorage.setItem("userName_",that.loginForm.username);
                                            that.$router.push('editPwd');
                                        }
                                    });

                                    return;
                                }
                                that.$message({
                                    type: 'success',
                                    message: '登录成功'
                                });
                                window.localStorage.setItem("token",res.value.token);
                                window.localStorage.setItem("userName_",that.loginForm.username);
                                that.$router.push('ymSys/infoList');
                            }
                        )
                        
                        
					} else {
						this.$notify.error({
							title: '错误',
							message: '请输入正确的用户名密码',
							offset: 100
						});
						return false;
					}
				});
            },
            gotoEditPwd(){

                this.$router.push('editPwd');

            }
		},
		watch: {
		// 	adminInfo: function (newValue){
		// 		debugger;
		// 		if (newValue.id) {
		// 			this.$message({
        //                 type: 'success',
        //                 message: '检测到您之前登录过，将自动登录'
        //             });
		// 			this.$router.push('appList')
		// 		}
		// 	}
		}
	}
</script>

<style lang="less" scoped>
	@import '../style/mixin';
	.login_page{
		background-color: #324057;
	}
	.manage_tip{
		position: absolute;
		width: 100%;
		top: -100px;
		left: 0;
		p{
			font-size: 34px;
			color: #fff;
		}
	}
	.form_contianer{
		.wh(320px, 210px);
		.ctp(370px, 260px);
		padding: 25px;
		border-radius: 5px;
		text-align: center;
		background-color: #fff;
		.submit_btn{
			width: 100%;
			font-size: 16px;
		}
	}
	.tip{
		font-size: 12px;
		color: red;
	}
	.form-fade-enter-active, .form-fade-leave-active {
	  	transition: all 1s;
	}
	.form-fade-enter, .form-fade-leave-active {
	  	transform: translate3d(0, -50px, 0);
	  	opacity: 0;
    }
    .linkP{
        float: right;
        font-size: 14px;
        color: #66b1ff;
        cursor: pointer;
    }
    .linkP:hover{
        opacity: .6;
    }
</style>
