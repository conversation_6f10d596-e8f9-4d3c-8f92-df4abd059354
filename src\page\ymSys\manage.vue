<!--
 * @Descripttion: 
 * @version: 
 * @Author: chang<PERSON>
 * @Date: 2019-07-04 10:55:48
 * @LastEditors: changqing
 * @LastEditTime: 2020-10-30 17:06:21
 -->
<template>
	<div class="manage_page fillcontain">
        <!-- <el-radio-group v-model="isCollapse" style="margin-bottom: 20px;">
            <el-radio-button :label="false">展开</el-radio-button>
            <el-radio-button :label="true">收起</el-radio-button>
        </el-radio-group> -->
		<el-row style="height: 100%;">
	  		<el-col :span="4" class="menu_left_" style="height: 100%; background-color: #324057;overflow-x: hidden;">
				<el-menu 
					:default-active="defaultActive" 
					style="min-height: 100%;width:100%;min-width:205px;"  
					background-color="#324057"
      				text-color="#fff"
      				active-text-color="#ffd04b" router>
					<!-- <el-menu-item index="manage"><i class="el-icon-menu"></i>首页</el-menu-item> -->
					<el-submenu index="2">
						<template slot="title"><i class="el-icon-s-home"></i>首页</template>
						<el-menu-item index="/ymSys/infoList">基本信息管理</el-menu-item>
						<el-menu-item index="/ymSys/exportData">数据导出</el-menu-item>
						<el-menu-item index="/ymSys/impData">民科数据接口导入</el-menu-item>
						<el-menu-item index="/ymSys/qxList">医疗美容器械咨讯</el-menu-item>
						<el-menu-item index="/ymSys/caseList">非法医疗美容案例</el-menu-item>
					</el-submenu>
				</el-menu>
			</el-col>
			<el-col :span="20"  style="height: 100%;overflow: auto;" class="table_right_">
				<keep-alive>
				    <router-view></router-view>
				</keep-alive>
			</el-col>
		</el-row>
  	</div>
</template>

<script>
    export default {
        data(){
            return {
                // isCollapse: true
            };
        },
		computed: {
			defaultActive: function(){
                debugger
				return this.$route.path;
			}
        },
        methods: {
            
        },
        mounted(){
            // document.title = "展台后管";
            
        }
    }
</script>


<style lang="less" scoped>
	@import '../../style/mixin';
	.manage_page{
		
	}
</style>
